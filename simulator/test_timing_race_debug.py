#!/usr/bin/env python3
"""
时序竞争问题调试测试
追踪动态流添加后立即消失的问题
"""

import sys
import time
import logging
from typing import List, Dict

# 设置路径
sys.path.append('.')

from collective_communication.communication_simulator import CommunicationSimulator
from collective_communication.flow_generator import FlowGenerator
from routing.topology_manager import TopologyManager
from routing.prediction_service import PredictionService

# 配置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockJob:
    """模拟作业对象"""
    def __init__(self, name: str, num_replicas: int = 2):
        self.name = name
        self.num_replicas = num_replicas
        self.placement = tuple(1 for _ in range(num_replicas))
        self.atomic_bsz = 32
        self.accum_steps = 1
        self.profile = {
            'compute_time': 1.0,
            'sync_time': 0.1
        }

def test_dynamic_flow_lifecycle():
    """测试动态流的完整生命周期"""
    print("=== 动态流生命周期调试测试 ===")
    
    # 创建模拟器组件
    flow_generator = FlowGenerator()
    topology_manager = TopologyManager()
    prediction_service = PredictionService()
    
    comm_simulator = CommunicationSimulator(
        flow_generator, topology_manager, prediction_service
    )
    
    # 创建简单的测试作业
    test_job = MockJob("debug-job", num_replicas=2)
    
    # 生成初始流
    placement = {test_job.name: [0, 1]}
    initial_flows = flow_generator.generate_all_potential_flows([test_job], placement, topology_manager)
    
    print(f"生成初始流: {len(initial_flows)} 个")
    for flow in initial_flows:
        print(f"  - {flow['flow_id']}: 轮次 {flow['round_idx']}, 状态 {flow['status']}")
    
    # 设置到模拟器
    comm_simulator.set_all_known_flows(initial_flows)
    print(f"设置后FlowManager中流数量: {comm_simulator.flow_manager.get_flow_count()}")
    
    # 注册作业
    comm_simulator.job_registry[test_job.name] = test_job
    
    # 启用多次Ring All-Reduce模式
    comm_simulator.enable_multiple_allreduces = True
    comm_simulator.set_interval_start_time(0.0)
    
    # 初始化模拟
    comm_simulator._initialize_simulation([test_job], {})
    print(f"初始化后FlowManager中流数量: {comm_simulator.flow_manager.get_flow_count()}")
    
    # 准备初始事件
    comm_simulator._prepare_initial_events([test_job])
    
    # 运行短时间模拟来观察动态流的行为
    print("\n--- 开始模拟 ---")
    try:
        # 运行5秒的模拟
        communication_times = comm_simulator.run({}, [test_job], 5.0)
        print(f"模拟结果: {communication_times}")
        
        # 检查最终状态
        final_flow_count = comm_simulator.flow_manager.get_flow_count()
        print(f"模拟结束后FlowManager中流数量: {final_flow_count}")
        
        # 显示FlowManager统计
        stats = comm_simulator.flow_manager.get_statistics()
        print(f"FlowManager统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"模拟过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_deletion_timing():
    """测试批量删除的时机"""
    print("\n=== 批量删除时机测试 ===")
    
    # 创建模拟器
    flow_generator = FlowGenerator()
    topology_manager = TopologyManager()
    prediction_service = PredictionService()
    
    comm_simulator = CommunicationSimulator(
        flow_generator, topology_manager, prediction_service
    )
    
    # 创建测试流
    test_flows = []
    for round_idx in range(2):
        for flow_idx in range(2):
            flow = {
                'flow_id': f'timing-test_round{round_idx}_flow{flow_idx}',
                'job_name': 'timing-test',
                'round_idx': round_idx,
                'status': 'COMPLETED' if round_idx == 0 else 'PLANNED',
                'flow_features': [5.0],
                'path': ['H1', 'S1', 'H2']
            }
            test_flows.append(flow)
    
    # 设置流
    comm_simulator.set_all_known_flows(test_flows)
    print(f"设置测试流: {len(test_flows)} 个")
    
    # 显示初始状态
    for flow in test_flows:
        print(f"  - {flow['flow_id']}: 轮次 {flow['round_idx']}, 状态 {flow['status']}")
    
    # 测试轮次完成追踪
    print("\n--- 测试轮次1完成 ---")
    comm_simulator._track_round_completion('timing-test', 1)
    
    # 检查删除结果
    remaining_flows = comm_simulator.flow_manager.get_all_flows()
    print(f"轮次1完成后剩余流: {len(remaining_flows)} 个")
    for flow in remaining_flows:
        print(f"  - {flow['flow_id']}: 轮次 {flow['round_idx']}, 状态 {flow['status']}")
    
    return True

def test_flow_id_pattern_analysis():
    """分析流ID模式"""
    print("\n=== 流ID模式分析 ===")
    
    # 分析日志中出现的流ID
    problematic_flow_ids = [
        "cifar10-0_round0_srcH1_dstH2",
        "cifar10-0_round0_srcH2_dstH1",
        "cifar10-3_round0_srcH1_dstH2",
        "cifar10-3_round0_srcH2_dstH1"
    ]
    
    for flow_id in problematic_flow_ids:
        parts = flow_id.split('_')
        if len(parts) >= 4:
            job_name = '_'.join(parts[:-3])
            round_part = parts[-3]
            src_part = parts[-2]
            dst_part = parts[-1]
            
            print(f"流ID: {flow_id}")
            print(f"  作业: {job_name}")
            print(f"  轮次: {round_part}")
            print(f"  源: {src_part}")
            print(f"  目标: {dst_part}")
            print()
    
    return True

if __name__ == "__main__":
    print("开始时序竞争问题调试测试...\n")
    
    # 运行调试测试
    test_results = []
    
    test_results.append(("流ID模式分析", test_flow_id_pattern_analysis()))
    test_results.append(("批量删除时机测试", test_batch_deletion_timing()))
    test_results.append(("动态流生命周期测试", test_dynamic_flow_lifecycle()))
    
    # 总结结果
    print("\n" + "="*50)
    print("调试测试结果总结:")
    
    for test_name, result in test_results:
        status = "✅ 完成" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print("\n📝 请查看详细的DEBUG日志来分析问题根源。")

#!/usr/bin/env python3
"""
批量删除机制测试脚本
验证轮次批量删除功能的正确性和性能
"""

import sys
import time
import logging
from typing import List, Dict

# 设置路径
sys.path.append('.')

from collective_communication.communication_simulator import CommunicationSimulator
from collective_communication.flow_generator import FlowGenerator
from routing.topology_manager import TopologyManager
from routing.prediction_service import PredictionService

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_flows(job_name: str, total_rounds: int, flows_per_round: int) -> List[Dict]:
    """创建测试流数据"""
    flows = []
    
    for round_idx in range(total_rounds):
        for flow_idx in range(flows_per_round):
            flow = {
                'flow_id': f'{job_name}_round{round_idx}_flow{flow_idx}',
                'job_name': job_name,
                'round_idx': round_idx,
                'status': 'PLANNED',
                'start_time': round_idx * 2.0 + flow_idx * 0.001,
                'flow_features': [10.0],
                'path': ['H1', 'S1', 'P1', 'S2', 'H2'],
                'model': 'test_model',
                'dataset': 'test_dataset',
                'parameters': 1000000.0
            }
            flows.append(flow)
    
    return flows

def test_batch_deletion_mechanism():
    """测试批量删除机制"""
    print("=== 批量删除机制测试 ===")
    
    # 创建模拟器组件
    flow_generator = FlowGenerator()
    topology_manager = TopologyManager()
    prediction_service = PredictionService()
    
    comm_simulator = CommunicationSimulator(
        flow_generator, topology_manager, prediction_service
    )
    
    # 创建测试数据
    job_name = "test_job"
    total_rounds = 4
    flows_per_round = 3
    test_flows = create_test_flows(job_name, total_rounds, flows_per_round)
    
    print(f"创建测试流: {len(test_flows)} 个流，{total_rounds} 轮，每轮 {flows_per_round} 个流")
    
    # 设置初始流
    comm_simulator.set_all_known_flows(test_flows)
    initial_count = len(comm_simulator.all_known_flows)
    print(f"初始流数量: {initial_count}")
    
    # 模拟流完成和轮次完成
    for round_idx in range(total_rounds):
        print(f"\n--- 测试轮次 {round_idx} ---")
        
        # 将该轮次的流标记为已完成
        for flow in comm_simulator.all_known_flows:
            if (flow.get('job_name') == job_name and 
                flow.get('round_idx') == round_idx):
                flow['status'] = 'COMPLETED'
        
        flows_before = len(comm_simulator.all_known_flows)
        print(f"轮次 {round_idx} 完成前流数量: {flows_before}")
        
        # 触发轮次完成追踪
        comm_simulator._track_round_completion(job_name, round_idx)
        
        flows_after = len(comm_simulator.all_known_flows)
        print(f"轮次 {round_idx} 完成后流数量: {flows_after}")
        
        # 验证删除逻辑
        if round_idx > 0:  # 从第二轮开始应该有删除
            expected_deleted = flows_per_round  # 应该删除上一轮的流
            actual_deleted = flows_before - flows_after
            print(f"预期删除: {expected_deleted}, 实际删除: {actual_deleted}")
            
            if actual_deleted == expected_deleted:
                print("✅ 删除数量正确")
            else:
                print("❌ 删除数量不正确")
        else:
            print("第一轮不删除流")
    
    # 检查最终状态
    print(f"\n=== 最终状态 ===")
    final_count = len(comm_simulator.all_known_flows)
    print(f"最终流数量: {final_count}")
    
    # 验证剩余流的状态
    active_flows = [f for f in comm_simulator.all_known_flows if f.get('status') != 'COMPLETED']
    completed_flows = [f for f in comm_simulator.all_known_flows if f.get('status') == 'COMPLETED']
    
    print(f"活跃流数量: {len(active_flows)}")
    print(f"已完成流数量: {len(completed_flows)}")
    
    # 显示删除统计
    stats = comm_simulator.deletion_stats
    print(f"\n=== 删除统计 ===")
    print(f"总删除次数: {stats['total_deletions']}")
    print(f"总删除流数: {stats['total_deleted_flows']}")
    print(f"总删除时间: {stats['total_deletion_time']*1000:.2f}ms")
    print(f"删除前最大流数: {stats['max_flows_before_deletion']}")
    print(f"删除后最小流数: {stats['min_flows_after_deletion']}")
    
    if stats['total_deletions'] > 0:
        avg_time = stats['total_deletion_time'] / stats['total_deletions']
        print(f"平均删除时间: {avg_time*1000:.2f}ms")

def test_performance_improvement():
    """测试性能改进效果"""
    print("\n=== 性能改进测试 ===")
    
    # 创建大规模测试数据
    large_flows = []
    for job_idx in range(3):  # 3个作业
        job_name = f"job_{job_idx}"
        for round_idx in range(6):  # 每个作业6轮
            for flow_idx in range(4):  # 每轮4个流
                flow = {
                    'flow_id': f'{job_name}_r{round_idx}_f{flow_idx}',
                    'job_name': job_name,
                    'round_idx': round_idx,
                    'status': 'COMPLETED' if round_idx < 4 else 'ACTIVE',  # 前4轮已完成
                    'flow_features': [15.0],
                    'path': ['H1', 'S1', 'P1', 'S2', 'H2']
                }
                large_flows.append(flow)
    
    print(f"创建大规模测试数据: {len(large_flows)} 个流")
    
    # 测试遍历性能（模拟特征计算）
    def simulate_feature_calculation(flows):
        """模拟特征计算的遍历操作"""
        active_count = 0
        for flow in flows:
            if flow.get('status') != 'COMPLETED':
                # 模拟一些计算操作
                _ = flow.get('flow_features', [0])[0] * 2
                active_count += 1
        return active_count
    
    # 测试删除前的性能
    start_time = time.time()
    iterations = 1000
    for _ in range(iterations):
        active_count = simulate_feature_calculation(large_flows)
    before_time = time.time() - start_time
    
    # 模拟删除已完成流
    active_flows = [f for f in large_flows if f.get('status') != 'COMPLETED']
    
    # 测试删除后的性能
    start_time = time.time()
    for _ in range(iterations):
        active_count = simulate_feature_calculation(active_flows)
    after_time = time.time() - start_time
    
    print(f"删除前流数量: {len(large_flows)}")
    print(f"删除后流数量: {len(active_flows)}")
    print(f"删除前遍历时间: {before_time*1000:.2f}ms ({iterations} 次)")
    print(f"删除后遍历时间: {after_time*1000:.2f}ms ({iterations} 次)")
    
    if after_time > 0:
        improvement = before_time / after_time
        print(f"性能提升: {improvement:.2f}x")
        memory_saving = (len(large_flows) - len(active_flows)) / len(large_flows) * 100
        print(f"内存节省: {memory_saving:.1f}%")

if __name__ == "__main__":
    test_batch_deletion_mechanism()
    test_performance_improvement()
    print("\n✅ 批量删除机制测试完成")

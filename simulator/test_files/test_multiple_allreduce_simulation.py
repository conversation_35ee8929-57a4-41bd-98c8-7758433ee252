#!/usr/bin/env python3
"""
多次Ring All-Reduce模拟测试 - 验证在60秒间隔内模拟多次通信的功能
"""

import sys
import os
import logging

# 添加simulator目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from simulator import Simulator
from pollux import PolluxPolicy
import pandas as pd

def test_multiple_allreduce_simulation():
    """测试多次Ring All-Reduce模拟功能"""
    print("=== 多次Ring All-Reduce模拟测试 ===")
    
    # 创建测试工作负载
    workload_data = {
        'name': ['job1', 'job2'],
        'application': ['resnet50', 'bert'],
        'time': [0, 5],
        'batch_size': [32, 16]
    }
    workload = pd.DataFrame(workload_data)
    
    # 创建集群模拟器
    policy = PolluxPolicy()
    cluster = Simulator(
        workload=workload,
        policy=policy,
        num_nodes=4,
        enable_routing_optimizer=True
    )
    
    # 设置作业placement（跨节点）
    cluster.jobs[0].placement = (2, 2)  # job1跨2个节点，Ring大小=2，总轮次=2*(2-1)=2
    cluster.jobs[1].placement = (1, 1, 1)  # job2跨3个节点，Ring大小=3，总轮次=2*(3-1)=4
    
    # 设置allocations
    cluster.allocations = {
        'job1': [0, 0, 1, 1],
        'job2': [1, 2, 3]
    }
    
    print("作业配置:")
    for job in cluster.jobs:
        ring_size = len(job.placement) if job.placement else 0
        total_rounds = 2 * (ring_size - 1) if ring_size > 1 else 0
        print(f"  {job.name}: placement={job.placement}, Ring大小={ring_size}, 总轮次={total_rounds}")
    
    # 启用详细日志
    logging.getLogger('collective_communication.communication_simulator').setLevel(logging.INFO)
    logging.getLogger('collective_communication.ring_all_reduce_tracker').setLevel(logging.INFO)
    logging.getLogger('collective_communication.flow_generator').setLevel(logging.DEBUG)
    
    # 执行60秒模拟
    print("\n=== 执行60秒模拟 ===")
    cluster.step(60)
    
    # 检查通信时间结果
    comm_times = cluster.communication_times
    print(f"\n通信时间结果: {comm_times}")
    
    # 分析结果
    for job_name, comm_time in comm_times.items():
        print(f"作业 {job_name}: 通信时间 = {comm_time:.4f}秒")
        
        if comm_time > 0:
            print(f"  ✅ 作业 {job_name} 产生了非零通信时间")
        else:
            print(f"  ⚠️  作业 {job_name} 通信时间为0")
    
    # 验证状态保存
    print(f"\n状态保存检查:")
    print(f"  保存的通信状态: {cluster.saved_communication_state is not None}")
    print(f"  上一个placement: {cluster.previous_placement}")
    
    print("✅ 多次Ring All-Reduce模拟测试完成")
    return True

def test_single_vs_multi_node_jobs():
    """测试单节点vs多节点作业的通信时间差异"""
    print("\n=== 单节点vs多节点作业测试 ===")
    
    # 创建测试工作负载
    workload_data = {
        'name': ['single_node_job', 'multi_node_job'],
        'application': ['resnet50', 'resnet50'],
        'time': [0, 0],
        'batch_size': [32, 32]
    }
    workload = pd.DataFrame(workload_data)
    
    # 创建集群模拟器
    policy = PolluxPolicy()
    cluster = Simulator(
        workload=workload,
        policy=policy,
        num_nodes=4,
        enable_routing_optimizer=True
    )
    
    # 设置作业placement
    cluster.jobs[0].placement = (4,)  # 单节点作业
    cluster.jobs[1].placement = (2, 2)  # 多节点作业
    
    # 设置allocations
    cluster.allocations = {
        'single_node_job': [0, 0, 0, 0],
        'multi_node_job': [0, 0, 1, 1]
    }
    
    print("作业配置:")
    for job in cluster.jobs:
        unique_nodes = len(set(cluster.allocations.get(job.name, [])))
        job_type = "单节点" if unique_nodes <= 1 else "多节点"
        print(f"  {job.name}: placement={job.placement}, 类型={job_type}")
    
    # 执行模拟
    cluster.step(60)
    
    # 检查结果
    comm_times = cluster.communication_times
    print(f"\n通信时间结果:")
    
    single_node_time = comm_times.get('single_node_job', -1)
    multi_node_time = comm_times.get('multi_node_job', -1)
    
    print(f"  单节点作业通信时间: {single_node_time:.4f}秒")
    print(f"  多节点作业通信时间: {multi_node_time:.4f}秒")
    
    # 验证预期结果
    if single_node_time == 0.0:
        print("  ✅ 单节点作业通信时间为0（符合预期）")
    else:
        print("  ❌ 单节点作业通信时间不为0（不符合预期）")
    
    if multi_node_time > 0:
        print("  ✅ 多节点作业通信时间大于0（符合预期）")
    else:
        print("  ❌ 多节点作业通信时间不大于0（不符合预期）")
    
    print("✅ 单节点vs多节点作业测试完成")
    return True

def test_communication_time_consistency():
    """测试通信时间的一致性"""
    print("\n=== 通信时间一致性测试 ===")
    
    # 创建相同配置的作业
    workload_data = {
        'name': ['job1', 'job2'],
        'application': ['resnet50', 'resnet50'],
        'time': [0, 0],
        'batch_size': [32, 32]
    }
    workload = pd.DataFrame(workload_data)
    
    policy = PolluxPolicy()
    cluster = Simulator(
        workload=workload,
        policy=policy,
        num_nodes=4,
        enable_routing_optimizer=True
    )
    
    # 设置相同的placement
    cluster.jobs[0].placement = (2, 2)
    cluster.jobs[1].placement = (2, 2)
    
    cluster.allocations = {
        'job1': [0, 0, 1, 1],
        'job2': [2, 2, 3, 3]
    }
    
    print("两个作业使用相同的placement配置: (2, 2)")
    
    # 执行模拟
    cluster.step(60)
    
    # 检查结果一致性
    comm_times = cluster.communication_times
    job1_time = comm_times.get('job1', -1)
    job2_time = comm_times.get('job2', -1)
    
    print(f"作业1通信时间: {job1_time:.4f}秒")
    print(f"作业2通信时间: {job2_time:.4f}秒")
    
    # 检查是否相近（允许小的差异）
    if abs(job1_time - job2_time) < 0.1:
        print("✅ 相同配置的作业通信时间基本一致")
    else:
        print("⚠️  相同配置的作业通信时间差异较大")
    
    print("✅ 通信时间一致性测试完成")
    return True

if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
    
    try:
        # 运行测试
        test_multiple_allreduce_simulation()
        test_single_vs_multi_node_jobs()
        test_communication_time_consistency()
        print("\n🎉 所有测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

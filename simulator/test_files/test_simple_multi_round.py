#!/usr/bin/env python3
"""
简单的多轮次验证测试
验证修复后的事件驱动模拟是否能进行多轮通信
"""

import sys
import os
import logging

# 设置路径
sys.path.append(os.path.dirname(__file__))

def test_simple_multi_round():
    """简单的多轮次验证"""
    print("=== 简单多轮次验证测试 ===")
    
    try:
        import pandas as pd
        from simulator import Cluster
        from pollux import PolluxPolicy
        
        # 创建简单的多节点工作负载
        workload_data = {
            'name': ['test_job'],
            'application': ['cifar10'],
            'time': [0]
        }
        workload = pd.DataFrame(workload_data)
        
        policy = PolluxPolicy()
        cluster = Cluster(workload, policy, min_nodes=3, num_gpus=4)
        
        # 设置多节点placement
        for job in cluster.jobs:
            if job.name == 'test_job':
                job.reallocate([2, 2])  # 2个节点，每个节点2个GPU
        
        cluster.allocations = {
            'test_job': [0, 0, 1, 1]
        }
        
        print("设置的placement:", {job.name: job.placement for job in cluster.jobs})
        print("设置的allocations:", cluster.allocations)
        
        # 启用详细日志
        logging.getLogger('collective_communication.communication_simulator').setLevel(logging.INFO)
        logging.getLogger('collective_communication.ring_all_reduce_tracker').setLevel(logging.INFO)
        logging.getLogger('collective_communication.flow_generator').setLevel(logging.DEBUG)
        
        # 执行模拟
        print("\n执行60秒模拟...")
        cluster.step(60)
        
        # 检查结果
        comm_times = cluster.communication_times
        print(f"\n通信时间结果: {comm_times}")
        
        # 分析结果
        for job_name, comm_time in comm_times.items():
            print(f"作业 {job_name}: 通信时间 = {comm_time:.4f}秒")
            
            if comm_time > 0:
                print(f"✅ 作业 {job_name} 产生了非零通信时间")
            else:
                print(f"⚠️  作业 {job_name} 通信时间为0")
        
        print("✅ 简单多轮次验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 简单多轮次验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 简单多轮次验证测试")
    
    # 设置基础日志级别
    logging.basicConfig(level=logging.WARNING)
    
    if test_simple_multi_round():
        print("\n🎉 验证通过！")
    else:
        print("\n❌ 验证失败！") 
#!/usr/bin/env python3
"""
通信时间计算修复验证测试
验证第一轮开始时间记录和通信时间计算的正确性
"""

import sys
import os
import logging
import pandas as pd

# 设置路径
sys.path.append(os.path.dirname(__file__))

def test_communication_time_calculation():
    """测试通信时间计算是否正确"""
    print("=== 测试通信时间计算修复 ===")
    
    try:
        from simulator import Cluster
        from pollux import PolluxPolicy
        
        # 创建简单的多节点工作负载
        workload_data = {
            'name': ['comm_time_test'],
            'application': ['cifar10'],
            'time': [0]
        }
        workload = pd.DataFrame(workload_data)
        
        policy = PolluxPolicy()
        cluster = Cluster(workload, policy, min_nodes=3, num_gpus=4)
        
        # 设置多节点placement
        for job in cluster.jobs:
            if job.name == 'comm_time_test':
                job.reallocate([2, 2])  # 2个节点，每个节点2个GPU
        
        cluster.allocations = {
            'comm_time_test': [0, 0, 1, 1]
        }
        
        print("测试配置:")
        print(f"  作业placement: {cluster.jobs[0].placement}")
        print(f"  节点分配: {cluster.allocations}")
        
        # 启用详细日志查看时间记录过程
        logging.getLogger('collective_communication.ring_all_reduce_tracker').setLevel(logging.DEBUG)
        logging.getLogger('collective_communication.communication_simulator').setLevel(logging.INFO)
        
        # 执行模拟
        print("\n执行60秒模拟...")
        start_wall_time = cluster.current_time
        cluster.step(60)
        end_wall_time = cluster.current_time
        
        # 检查结果
        comm_times = cluster.communication_times
        print(f"\n通信时间结果:")
        
        for job_name, comm_time in comm_times.items():
            print(f"  作业 {job_name}: {comm_time:.4f}秒")
            
            # 验证通信时间
            if comm_time > 0:
                print(f"✅ 通信时间计算成功，非零值: {comm_time:.4f}秒")
                
                # 基本合理性检查
                if 0.1 <= comm_time <= 60.0:  # 应该在合理范围内
                    print(f"✅ 通信时间在合理范围内: {comm_time:.4f}秒")
                else:
                    print(f"⚠️  通信时间可能异常: {comm_time:.4f}秒")
                    
                return True
            else:
                print(f"❌ 通信时间仍为0，修复可能失败")
                return False
        
        print("✅ 通信时间计算测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 通信时间计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_first_round_time_recording():
    """测试第一轮开始时间记录机制"""
    print("\n=== 测试第一轮开始时间记录 ===")
    
    try:
        from collective_communication.ring_all_reduce_tracker import RingAllReduceTracker
        from collective_communication.flow_generator import FlowGenerator
        from routing.topology_manager import TopologyManager
        
        # 初始化组件
        flow_generator = FlowGenerator()
        topology_manager = TopologyManager()
        tracker = RingAllReduceTracker(flow_generator, topology_manager)
        
        # 创建测试作业
        class MockJob:
            def __init__(self, name):
                self.name = name
                self.placement = (2, 2)
                self.application = MockApplication()
        
        class MockApplication:
            def __init__(self):
                self.name = "cifar10"
                self.parameters = ********
        
        job = MockJob("time_record_test")
        ring_size = 2
        
        # 创建第一轮流
        first_round_flows = [
            {"flow_id": "flow_0_0", "job_name": "time_record_test", "round_idx": 0},
            {"flow_id": "flow_0_1", "job_name": "time_record_test", "round_idx": 0}
        ]
        
        # 初始化作业
        tracker.initialize_job(job, ring_size, first_round_flows)
        # print(f"初始化作业 {job.name}，Ring大小={ring_size}")
        
        # 测试开始通信时间记录
        start_time = 10.5
        tracker.start_job_communication(job.name, start_time)
        
        # 验证第一轮开始时间是否正确记录
        recorded_times = tracker.job_round_start_times.get(job.name, {})
        print(f"记录的轮次开始时间: {recorded_times}")
        
        if 0 in recorded_times and recorded_times[0] == start_time:
            print(f"✅ 第一轮开始时间正确记录: {recorded_times[0]}")
            
            # 模拟作业完成，测试通信时间计算
            end_time = 15.8
            total_expected_time = end_time - start_time
            
            # 直接调用计算方法
            tracker._complete_job_communication(job.name, end_time)
            
            calculated_time = tracker.get_job_communication_time(job.name)
            print(f"计算的通信时间: {calculated_time:.4f}秒")
            print(f"期望的通信时间: {total_expected_time:.4f}秒")
            
            if abs(calculated_time - total_expected_time) < 0.001:
                print("✅ 通信时间计算正确")
                return True
            else:
                print(f"❌ 通信时间计算错误，差异: {abs(calculated_time - total_expected_time):.4f}秒")
                return False
        else:
            print(f"❌ 第一轮开始时间记录失败，期望{start_time}，实际{recorded_times.get(0, 'None')}")
            return False
        
    except Exception as e:
        print(f"❌ 第一轮时间记录测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_calculation_methods():
    """测试多种通信时间计算方法的鲁棒性"""
    print("\n=== 测试多种计算方法鲁棒性 ===")
    
    try:
        from collective_communication.ring_all_reduce_tracker import RingAllReduceTracker
        from collective_communication.flow_generator import FlowGenerator
        from routing.topology_manager import TopologyManager
        
        flow_generator = FlowGenerator()
        topology_manager = TopologyManager()
        tracker = RingAllReduceTracker(flow_generator, topology_manager)
        
        class MockJob:
            def __init__(self, name):
                self.name = name
                self.placement = (2, 2)
        
        job = MockJob("robust_test")
        job_name = job.name
        
        # 手动设置状态模拟不同情况
        tracker.job_status[job_name] = tracker.job_status.get(job_name, {})
        tracker.job_round_start_times[job_name] = {}
        tracker.job_round_end_times[job_name] = {}
        tracker.job_communication_times[job_name] = 0.0
        
        print("测试方案1: 第一轮时间正常记录")
        tracker.job_round_start_times[job_name][0] = 5.0
        tracker._complete_job_communication(job_name, 10.0)
        time1 = tracker.get_job_communication_time(job_name)
        print(f"  方案1结果: {time1:.4f}秒 (期望5.0秒)")
        
        # 重置状态测试方案3
        tracker.job_communication_times[job_name] = 0.0
        tracker.job_round_start_times[job_name] = {1: 6.0, 2: 8.0}  # 缺少第一轮，但有其他轮次
        
        print("测试方案3: 从其他轮次时间反推")
        tracker._complete_job_communication(job_name, 12.0)
        time3 = tracker.get_job_communication_time(job_name)
        print(f"  方案3结果: {time3:.4f}秒 (期望6.0秒，从最早轮次开始)")
        
        if time1 == 5.0 and time3 == 6.0:
            print("✅ 多种计算方法都工作正常")
            return True
        else:
            print(f"❌ 计算方法有问题：方案1={time1}, 方案3={time3}")
            return False
        
    except Exception as e:
        print(f"❌ 多种计算方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 开始通信时间计算修复验证测试")
    
    # 设置基础日志级别
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        ("通信时间计算修复", test_communication_time_calculation),
        ("第一轮时间记录机制", test_first_round_time_recording), 
        ("多种计算方法鲁棒性", test_multiple_calculation_methods)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"执行测试: {test_name}")
        print('='*60)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*60}")
    print(f"通信时间计算修复测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有通信时间计算修复测试通过！")
        print("📋 修复总结:")
        print("  ✅ 第一轮开始时间记录已修复")
        print("  ✅ 通信时间计算逻辑已完善")
        print("  ✅ 多种备选计算方法已实现")
        print("  ✅ 调试验证机制已增强")
        sys.exit(0)
    else:
        print("❌ 部分测试失败，需要进一步调试")
        sys.exit(1) 
#!/usr/bin/env python3
"""
Pollux策略batch size简化验证测试
"""

import sys
import os
import pandas as pd

# 添加simulator目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_pollux_batch_size_simplification():
    """验证Pollux策略的batch size简化"""
    print("=== Pollux Batch Size简化验证测试 ===")
    
    try:
        from simulator import Simulator
        from pollux import PolluxPolicy
        
        # 创建测试工作负载
        workload_data = {
            'name': ['cifar10_job', 'bert_job', 'ncf_job', 'deepspeech2_job'],
            'application': ['cifar10', 'bert', 'ncf', 'deepspeech2'],
            'time': [0, 0, 0, 0],
            'batch_size': [128, 32, 1024, 64]  # 这些值应该被忽略
        }
        workload = pd.DataFrame(workload_data)
        
        # 创建Pollux策略的集群模拟器
        policy = PolluxPolicy()
        cluster = Simulator(
            workload=workload,
            policy=policy,
            num_nodes=4
        )
        
        print("验证各作业的target_batch_size设置:")
        
        # 预期的最大batch size
        expected_max_batch_sizes = {
            'cifar10_job': 4096,
            'bert_job': 384,
            'ncf_job': 32768,
            'deepspeech2_job': 640
        }
        
        all_correct = True
        
        for job in cluster.jobs:
            expected_max = expected_max_batch_sizes[job.name]
            actual_target = job.target_batch_size
            
            print(f"  {job.name}:")
            print(f"    应用最大batch size: {job.application.max_batch_size}")
            print(f"    设置的target_batch_size: {actual_target}")
            print(f"    预期值: {expected_max}")
            
            if actual_target == expected_max:
                print(f"    ✅ target_batch_size设置正确")
            else:
                print(f"    ❌ target_batch_size设置错误")
                all_correct = False
        
        if all_correct:
            print("✅ 所有作业的target_batch_size都设置为最大值")
        else:
            print("❌ 部分作业的target_batch_size设置错误")
            return False
        
        print("\n验证累积梯度步数:")
        
        # 测试update_local_bsz方法
        for job in cluster.jobs:
            # 模拟不同的placement
            test_placements = [
                (4,),      # 单节点4GPU
                (2, 2),    # 双节点各2GPU
                (1, 1, 1, 1)  # 四节点各1GPU
            ]
            
            print(f"  {job.name}:")
            
            for placement in test_placements:
                job.update_local_bsz(placement)
                
                print(f"    placement {placement}: accum_steps={job.accum_steps}, atomic_bsz={job.atomic_bsz}")
                
                if job.accum_steps == 1:
                    print(f"      ✅ 累积梯度步数固定为1")
                else:
                    print(f"      ❌ 累积梯度步数不是1: {job.accum_steps}")
                    all_correct = False
        
        if all_correct:
            print("✅ 所有作业的累积梯度步数都固定为1")
        else:
            print("❌ 部分作业的累积梯度步数不是1")
            return False
        
        print("✅ Pollux Batch Size简化验证测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_atomic_bsz_calculation():
    """验证atomic_bsz计算的正确性"""
    print("\n=== Atomic Batch Size计算验证 ===")
    
    try:
        from simulator import Simulator
        from pollux import PolluxPolicy
        
        # 创建简单测试工作负载
        workload_data = {
            'name': ['test_job'],
            'application': ['cifar10'],
            'time': [0]
        }
        workload = pd.DataFrame(workload_data)
        
        policy = PolluxPolicy()
        cluster = Simulator(workload=workload, policy=policy, num_nodes=2)
        
        job = cluster.jobs[0]
        
        print(f"测试作业: {job.name}")
        print(f"最大batch size: {job.application.max_batch_size}")
        print(f"target_batch_size: {job.target_batch_size}")
        
        # 测试不同placement下的计算
        test_cases = [
            ((4,), "单节点4GPU"),
            ((2, 2), "双节点各2GPU"),
            ((1, 1), "双节点各1GPU")
        ]
        
        for placement, description in test_cases:
            job.update_local_bsz(placement)
            
            num_replicas = sum(placement)
            expected_local_bsz = job.target_batch_size / num_replicas
            expected_atomic_bsz = expected_local_bsz / (job.accum_steps + 1)
            
            print(f"\n{description} {placement}:")
            print(f"  num_replicas: {num_replicas}")
            print(f"  accum_steps: {job.accum_steps}")
            print(f"  atomic_bsz: {job.atomic_bsz}")
            print(f"  预期local_bsz: {expected_local_bsz:.2f}")
            print(f"  预期atomic_bsz: {expected_atomic_bsz:.2f}")
            
            # 验证总batch size
            total_batch_size = num_replicas * job.atomic_bsz * (job.accum_steps + 1)
            print(f"  实际总batch size: {total_batch_size}")
            print(f"  目标总batch size: {job.target_batch_size}")
            
            if abs(total_batch_size - job.target_batch_size) < 1e-6:
                print(f"  ✅ 总batch size计算正确")
            else:
                print(f"  ❌ 总batch size计算错误")
                return False
        
        print("✅ Atomic Batch Size计算验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """简单的性能对比测试"""
    print("\n=== 性能对比测试 ===")
    
    try:
        import time
        from simulator import Simulator
        from pollux import PolluxPolicy
        
        # 创建测试工作负载
        workload_data = {
            'name': ['job1', 'job2'],
            'application': ['cifar10', 'bert'],
            'time': [0, 0]
        }
        workload = pd.DataFrame(workload_data)
        
        policy = PolluxPolicy()
        cluster = Simulator(workload=workload, policy=policy, num_nodes=2)
        
        print("测试Job初始化和batch size设置的性能...")
        
        start_time = time.time()
        
        # 模拟多次placement更新
        for _ in range(10):
            for job in cluster.jobs:
                job.update_local_bsz((2, 2))
                job.update_local_bsz((1, 1, 1, 1))
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"20次update_local_bsz调用耗时: {elapsed_time:.4f}秒")
        
        if elapsed_time < 1.0:  # 应该很快
            print("✅ 性能表现良好")
        else:
            print("⚠️  性能可能需要进一步优化")
        
        print("✅ 性能对比测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("开始Pollux Batch Size简化验证测试...")
    
    tests = [
        test_pollux_batch_size_simplification,
        test_atomic_bsz_calculation,
        test_performance_comparison
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {e}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有Pollux Batch Size简化测试通过！")
        print("\n✅ 验证的修改：")
        print("  1. ✅ 所有作业的target_batch_size都设置为最大值")
        print("  2. ✅ 所有作业的累积梯度步数都固定为1")
        print("  3. ✅ atomic_bsz计算正确")
        print("  4. ✅ 性能表现良好")
        print("\n🎯 修改效果:")
        print("     避免了复杂的goodput优化调用")
        print("     简化了batch size相关计算")
        print("     保持了其他逻辑不变")
        return True
    else:
        print("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

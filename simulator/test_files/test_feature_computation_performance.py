#!/usr/bin/env python3
"""
特征计算性能测试脚本
统计预测模型中各个特征计算步骤的耗时
"""

import sys
import os
import time
import torch
import numpy as np
from typing import List, Dict

# 添加simulator目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from routing.prediction_service import PredictionService
from models.model import STGNNModel, FeatureEngineer
from routing.topology_manager import TopologyManager

def create_test_flows(num_flows: int = 10) -> tuple:
    """创建测试用的流数据"""
    flows_to_predict = []
    all_known_flows = []
    
    # 创建拓扑管理器来获取有效路径
    topology_manager = TopologyManager()
    
    for i in range(num_flows):
        # 创建测试流
        flow = {
            "flow_id": f"test_flow_{i}",
            "ringallreduce_group_size": 4,
            "flow_features": [10.0 + i],  # 流大小
            "path": ["H1", "S1", "P1", "S2", f"H{i%8+1}"],  # 测试路径
            "start_time": float(i * 5),  # 错开开始时间
            "model": "cifar10",
            "dataset": "test",
            "parameters": 1000000.0,
            "status": "PLANNED",
            "job_name": f"job_{i%3}",  # 3个不同的作业
            "round_idx": 0
        }
        
        flows_to_predict.append(flow)
        all_known_flows.append(flow)
    
    # 添加一些历史流作为背景
    for i in range(num_flows, num_flows + 20):
        background_flow = {
            "flow_id": f"background_flow_{i}",
            "ringallreduce_group_size": 4,
            "flow_features": [5.0 + i],
            "path": ["H2", "S2", "P1", "S1", f"H{i%8+1}"],
            "start_time": float(i * 2 - 30),  # 更早的开始时间
            "model": "bert",
            "dataset": "test",
            "parameters": 2000000.0,
            "status": "COMPLETED",
            "job_name": f"job_{i%3}",
            "round_idx": 0
        }
        all_known_flows.append(background_flow)
    
    return flows_to_predict, all_known_flows

def test_prediction_service_performance():
    """测试预测服务的整体性能"""
    print("=== 预测服务整体性能测试 ===")
    
    try:
        # 初始化预测服务
        print("初始化预测服务...")
        start_time = time.time()
        prediction_service = PredictionService()
        init_time = time.time() - start_time
        print(f"预测服务初始化耗时: {init_time:.4f}秒")
        
        # 测试不同批次大小的性能
        batch_sizes = [1, 5, 10, 20, 50]
        
        for batch_size in batch_sizes:
            print(f"\n--- 批次大小: {batch_size} ---")
            
            # 创建测试数据
            flows_to_predict, all_known_flows = create_test_flows(batch_size)
            
            # 预热（避免首次调用的初始化开销）
            prediction_service.predict_fct(flows_to_predict[:1], all_known_flows)
            
            # 正式测试
            num_runs = 5
            total_time = 0
            
            for run in range(num_runs):
                start_time = time.time()
                predictions = prediction_service.predict_fct(flows_to_predict, all_known_flows)
                end_time = time.time()
                
                run_time = end_time - start_time
                total_time += run_time
                print(f"  运行 {run+1}: {run_time:.4f}秒, 预测结果数量: {len(predictions)}")
            
            avg_time = total_time / num_runs
            per_flow_time = avg_time / batch_size
            print(f"  平均耗时: {avg_time:.4f}秒")
            print(f"  每个流平均耗时: {per_flow_time:.6f}秒")
            print(f"  吞吐量: {batch_size/avg_time:.2f} 流/秒")
        
        return True
        
    except Exception as e:
        print(f"预测服务性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_computation_breakdown():
    """详细测试特征计算各个步骤的耗时"""
    print("\n=== 特征计算详细性能分析 ===")
    
    try:
        # 初始化组件
        topology_manager = TopologyManager()
        feature_engineer = FeatureEngineer(
            clos_topology=topology_manager.clos_topology,
            future_window=2.0,
            time_slices=5
        )
        
        # 创建测试数据
        flows_to_predict, all_known_flows = create_test_flows(10)
        
        # 格式化流数据
        prediction_service = PredictionService()
        formatted_flows = []
        formatted_all_flows = []
        
        for flow in flows_to_predict:
            formatted_flow = prediction_service._format_flow_for_model(flow)
            formatted_flows.append(formatted_flow)
        
        for flow in all_known_flows:
            formatted_flow = prediction_service._format_flow_for_model(flow)
            formatted_all_flows.append(formatted_flow)
        
        print(f"测试数据: {len(formatted_flows)} 个待预测流, {len(formatted_all_flows)} 个总流")
        
        # 测试各个特征计算步骤
        num_runs = 10
        
        # 1. 空间特征计算
        print("\n1. 空间特征计算性能:")
        spatial_times = []
        for run in range(num_runs):
            start_time = time.time()
            for flow in formatted_flows:
                current_time = flow['inputs']['start_time']
                spatial_features = feature_engineer.compute_current_spatial_features(
                    current_time, flow, formatted_all_flows
                )
            end_time = time.time()
            spatial_times.append(end_time - start_time)
        
        avg_spatial_time = np.mean(spatial_times)
        print(f"  平均耗时: {avg_spatial_time:.4f}秒")
        print(f"  每个流平均: {avg_spatial_time/len(formatted_flows):.6f}秒")
        
        # 2. 未来特征计算
        print("\n2. 未来时间特征计算性能:")
        future_times = []
        for run in range(num_runs):
            start_time = time.time()
            for flow in formatted_flows:
                current_time = flow['inputs']['start_time']
                path = flow['inputs']['path']
                try:
                    path_links = feature_engineer.link_graph_builder.get_path_link_indices(path)
                    future_features = feature_engineer.compute_future_temporal_features(
                        path_links, current_time, formatted_all_flows
                    )
                except ValueError:
                    continue
            end_time = time.time()
            future_times.append(end_time - start_time)
        
        avg_future_time = np.mean(future_times)
        print(f"  平均耗时: {avg_future_time:.4f}秒")
        print(f"  每个流平均: {avg_future_time/len(formatted_flows):.6f}秒")
        
        # 3. 增量冲击特征计算
        print("\n3. 增量冲击特征计算性能:")
        proactive_times = []
        for run in range(num_runs):
            start_time = time.time()
            for flow in formatted_flows:
                proactive_features = feature_engineer.compute_proactive_contention_features(
                    flow, formatted_all_flows
                )
            end_time = time.time()
            proactive_times.append(end_time - start_time)
        
        avg_proactive_time = np.mean(proactive_times)
        print(f"  平均耗时: {avg_proactive_time:.4f}秒")
        print(f"  每个流平均: {avg_proactive_time/len(formatted_flows):.6f}秒")
        
        # 4. 流特征提取
        print("\n4. 流特征提取性能:")
        flow_feature_times = []
        for run in range(num_runs):
            start_time = time.time()
            for flow in formatted_flows:
                flow_features = feature_engineer.extract_flow_features(flow, formatted_all_flows)
            end_time = time.time()
            flow_feature_times.append(end_time - start_time)
        
        avg_flow_feature_time = np.mean(flow_feature_times)
        print(f"  平均耗时: {avg_flow_feature_time:.4f}秒")
        print(f"  每个流平均: {avg_flow_feature_time/len(formatted_flows):.6f}秒")
        
        # 总结
        total_feature_time = avg_spatial_time + avg_future_time + avg_proactive_time + avg_flow_feature_time
        print(f"\n=== 特征计算总结 ===")
        print(f"空间特征计算: {avg_spatial_time:.4f}秒 ({avg_spatial_time/total_feature_time*100:.1f}%)")
        print(f"未来特征计算: {avg_future_time:.4f}秒 ({avg_future_time/total_feature_time*100:.1f}%)")
        print(f"冲击特征计算: {avg_proactive_time:.4f}秒 ({avg_proactive_time/total_feature_time*100:.1f}%)")
        print(f"流特征提取: {avg_flow_feature_time:.4f}秒 ({avg_flow_feature_time/total_feature_time*100:.1f}%)")
        print(f"特征计算总耗时: {total_feature_time:.4f}秒")
        print(f"每个流特征计算平均耗时: {total_feature_time/len(formatted_flows):.6f}秒")
        
        return True
        
    except Exception as e:
        print(f"特征计算性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始特征计算性能测试...")
    
    # 测试预测服务整体性能
    success1 = test_prediction_service_performance()
    
    # 测试特征计算详细性能
    success2 = test_feature_computation_breakdown()
    
    if success1 and success2:
        print("\n✅ 所有性能测试完成")
    else:
        print("\n❌ 部分测试失败")

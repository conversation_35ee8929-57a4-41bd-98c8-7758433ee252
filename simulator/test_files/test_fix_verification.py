#!/usr/bin/env python3
"""
修复验证测试 - 验证关键问题是否已解决
"""

import sys
import os
import logging

# 设置路径
sys.path.append(os.path.dirname(__file__))

def test_ecmp_path_generation():
    """测试ECMP路径生成是否正确"""
    print("=== 测试ECMP路径生成修复 ===")
    
    try:
        from routing.topology_manager import TopologyManager
        
        # 初始化拓扑管理器
        topology_manager = TopologyManager()
        
        # 测试路径生成
        test_cases = [
            ("H1", "H5"),
            ("H2", "H8"),
            ("H1", "H16")
        ]
        
        for src, dst in test_cases:
            try:
                paths = topology_manager.get_ecmp_paths(src, dst)
                if paths:
                    print(f"✅ {src} -> {dst}: 找到 {len(paths)} 条路径")
                    print(f"   示例路径: {paths[0]}")
                    
                    # 验证是否为4跳路径
                    if len(paths[0]) == 5:
                        print(f"   ✅ 正确的4跳路径")
                    else:
                        print(f"   ❌ 路径长度错误: {len(paths[0])}")
                else:
                    print(f"❌ {src} -> {dst}: 未找到路径")
            except Exception as e:
                print(f"❌ {src} -> {dst}: 路径生成出错 - {e}")
        
        print("✅ ECMP路径生成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ ECMP路径生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_flow_generation():
    """测试流生成的主机名映射"""
    print("\n=== 测试流生成主机名映射 ===")
    
    try:
        from collective_communication.flow_generator import FlowGenerator
        from routing.topology_manager import TopologyManager
        
        # 创建模拟作业对象
        class MockJob:
            def __init__(self, name):
                self.name = name
                self.application = MockApplication()
        
        class MockApplication:
            def __init__(self):
                self.name = "cifar10"
                self.parameters = ********
        
        flow_generator = FlowGenerator()
        topology_manager = TopologyManager()
        
        # 模拟placement - 2个节点，每个节点2个GPU
        placement = {"test_job": [0, 0, 1, 1]}
        active_jobs = [MockJob("test_job")]
        
        # 生成流
        flows = flow_generator.generate_all_potential_flows(
            active_jobs, placement, topology_manager
        )
        
        if flows:
            print(f"✅ 成功生成 {len(flows)} 个流")
            
            # 检查主机名格式
            sample_flow = flows[0]
            src_host = sample_flow.get("src_host", "")
            dst_host = sample_flow.get("dst_host", "")
            
            if src_host.startswith('H') and dst_host.startswith('H'):
                print(f"✅ 主机名格式正确: {src_host} -> {dst_host}")
            else:
                print(f"❌ 主机名格式错误: {src_host} -> {dst_host}")
                
            # 检查路径是否有效
            path = sample_flow.get("path", [])
            if len(path) == 5:
                print(f"✅ 路径长度正确: {len(path)}")
            else:
                print(f"❌ 路径长度错误: {len(path)}")
        else:
            print("❌ 未生成任何流")
        
        print("✅ 流生成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 流生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prediction_service():
    """测试预测服务不再依赖time_delay"""
    print("\n=== 测试预测服务修复 ===")
    
    try:
        from routing.prediction_service import PredictionService
        
        prediction_service = PredictionService()
        
        # 创建测试流数据（不包含time_delay）
        test_flow = {
            "flow_id": "test_flow_1",
            "ringallreduce_group_size": 2,
            "flow_features": [10.0],
            "path": ["H1", "S1", "P1", "S2", "H5"],
            "start_time": 0.0,
            "model": "cifar10",
            "dataset": "test",
            "parameters": 1000000.0,
            "status": "PLANNED"
        }
        
        all_known_flows = [test_flow]
        flows_to_predict = [test_flow]
        
        # 尝试预测
        predictions = prediction_service.predict_fct(flows_to_predict, all_known_flows)
        
        if predictions and len(predictions) > 0:
            print(f"✅ 预测成功，FCT: {predictions[0]:.4f}秒")
        else:
            print("❌ 预测失败")
        
        print("✅ 预测服务测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 预测服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """简单的集成测试"""
    print("\n=== 测试整体集成 ===")
    
    try:
        import pandas as pd
        from simulator import Cluster
        from pollux import PolluxPolicy
        
        # 创建简单工作负载
        workload_data = {
            'name': ['test_job'],
            'application': ['cifar10'],
            'time': [0]
        }
        workload = pd.DataFrame(workload_data)
        
        # 创建集群（应该不出错）
        policy = PolluxPolicy()
        cluster = Cluster(workload, policy, min_nodes=2, num_gpus=4)
        
        print(f"✅ 集群创建成功，高保真通信: {cluster.enable_high_fidelity_comm}")
        
        if cluster.enable_high_fidelity_comm:
            print("✅ 高保真通信模拟已启用")
        else:
            print("❌ 高保真通信模拟未启用")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 开始修复验证测试")
    
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        ("ECMP路径生成", test_ecmp_path_generation),
        ("流生成主机名映射", test_flow_generation), 
        ("预测服务修复", test_prediction_service),
        ("整体集成", test_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"执行测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有修复验证测试通过！")
        sys.exit(0)
    else:
        print("❌ 部分测试失败，需要进一步修复")
        sys.exit(1) 
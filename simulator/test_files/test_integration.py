#!/usr/bin/env python3
"""
测试高保真通信模拟系统的集成
"""

import pandas as pd
import logging
import sys
import os

# 添加simulator目录到Python路径
sys.path.append(os.path.dirname(__file__))

from simulator import simulate
import argparse

def create_test_workload():
    """创建一个简单的测试工作负载"""
    workload_data = {
        'name': ['test_job_1', 'test_job_2'],
        'application': ['cifar10', 'deepspeech2'],
        'time': [0, 30],
        'num_replicas': [4, 8],
        'batch_size': [512, 1024]
    }
    
    workload_df = pd.DataFrame(workload_data)
    workload_df.to_csv('test_workload.csv', index=False)
    return 'test_workload.csv'

def test_high_fidelity_simulation():
    """测试高保真通信模拟"""
    print("=== 测试高保真通信模拟集成 ===")
    
    # 创建测试工作负载
    workload_path = create_test_workload()
    
    # 设置测试参数
    args = argparse.Namespace()
    args.workload = workload_path
    args.policy = 'pollux'
    args.min_nodes = 4
    args.max_nodes = None
    args.interval = 60
    args.interference = 0.0
    args.num_gpus = 4
    args.low_util = None
    args.high_util = None
    args.output = None
    
    try:
        print("启动模拟...")
        logs, jcts = simulate(args)
        
        print(f"模拟完成！")
        print(f"JCTs: {jcts}")
        print(f"日志条目数: {len(logs)}")
        
        # 清理测试文件
        os.remove(workload_path)
        
        print("✅ 高保真通信模拟集成测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 清理测试文件
        if os.path.exists(workload_path):
            os.remove(workload_path)
        
        return False

if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    success = test_high_fidelity_simulation()
    sys.exit(0 if success else 1) 
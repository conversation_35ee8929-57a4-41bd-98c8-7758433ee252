#!/usr/bin/env python3
"""
跨间隔通信测试 - 验证placement变化检测和状态持续功能
"""

import sys
import os
import logging

# 添加simulator目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from simulator import Simulator
from pollux import PolluxPolicy
import pandas as pd

def test_cross_interval_communication():
    """测试跨间隔通信功能"""
    print("=== 跨间隔通信测试 ===")
    
    # 创建测试工作负载
    workload_data = {
        'name': ['job1', 'job2'],
        'application': ['resnet50', 'bert'],
        'time': [0, 10],
        'batch_size': [32, 16]
    }
    workload = pd.DataFrame(workload_data)
    
    # 创建集群模拟器
    policy = PolluxPolicy()
    cluster = Simulator(
        workload=workload,
        policy=policy,
        num_nodes=4,
        enable_routing_optimizer=True
    )
    
    # 设置作业placement（跨节点）
    cluster.jobs[0].placement = (2, 2)  # job1跨2个节点
    cluster.jobs[1].placement = (1, 1, 1)  # job2跨3个节点
    
    # 设置allocations
    cluster.allocations = {
        'job1': [0, 0, 1, 1],
        'job2': [1, 2, 3]
    }
    
    print("初始placement设置:")
    for job in cluster.jobs:
        print(f"  {job.name}: {job.placement}")
    
    # 启用详细日志
    logging.getLogger('collective_communication.communication_simulator').setLevel(logging.INFO)
    logging.getLogger('collective_communication.ring_all_reduce_tracker').setLevel(logging.INFO)
    
    # 第一个60秒间隔
    print("\n=== 第一个60秒间隔 ===")
    cluster.step(60)
    
    # 检查是否保存了状态
    print(f"保存的通信状态: {cluster.saved_communication_state is not None}")
    print(f"上一个placement: {cluster.previous_placement}")
    
    # 第二个60秒间隔（placement不变）
    print("\n=== 第二个60秒间隔（placement不变）===")
    cluster.step(60)
    
    # 检查placement变化检测
    current_placement = {}
    for job in cluster.jobs:
        if job.placement and sum(job.placement) > 0:
            node_allocation = []
            current_node = 0
            for gpu_count in job.placement:
                for _ in range(gpu_count):
                    node_allocation.append(current_node)
                current_node += 1
            current_placement[job.name] = node_allocation
    
    changes = cluster._detect_placement_changes(current_placement)
    print(f"Placement变化检测: {changes}")
    
    # 第三个60秒间隔（修改placement）
    print("\n=== 第三个60秒间隔（修改placement）===")
    cluster.jobs[0].placement = (4,)  # job1改为单节点
    cluster.allocations['job1'] = [0, 0, 0, 0]
    
    cluster.step(60)
    
    # 再次检查placement变化
    new_placement = {}
    for job in cluster.jobs:
        if job.placement and sum(job.placement) > 0:
            node_allocation = []
            current_node = 0
            for gpu_count in job.placement:
                for _ in range(gpu_count):
                    node_allocation.append(current_node)
                current_node += 1
            new_placement[job.name] = node_allocation
    
    changes = cluster._detect_placement_changes(new_placement)
    print(f"修改后的placement变化检测: {changes}")
    
    print("✅ 跨间隔通信测试完成")
    return True

def test_placement_detection():
    """测试placement变化检测功能"""
    print("\n=== Placement变化检测测试 ===")
    
    # 创建简单的模拟器实例
    workload = pd.DataFrame({'name': ['test'], 'application': ['resnet50'], 'time': [0], 'batch_size': [32]})
    policy = PolluxPolicy()
    cluster = Simulator(workload=workload, policy=policy, num_nodes=2)
    
    # 测试相同placement
    old_placement = [0, 0, 1, 1]
    new_placement = [0, 0, 1, 1]
    result = cluster._is_placement_identical(old_placement, new_placement)
    print(f"相同placement检测: {result} (期望: True)")
    
    # 测试不同placement
    old_placement = [0, 0, 1, 1]
    new_placement = [0, 1, 1, 1]
    result = cluster._is_placement_identical(old_placement, new_placement)
    print(f"不同placement检测: {result} (期望: False)")
    
    # 测试None情况
    result = cluster._is_placement_identical(None, None)
    print(f"None placement检测: {result} (期望: True)")
    
    result = cluster._is_placement_identical([0, 1], None)
    print(f"一个None placement检测: {result} (期望: False)")
    
    print("✅ Placement变化检测测试完成")
    return True

if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
    
    try:
        # 运行测试
        test_placement_detection()
        test_cross_interval_communication()
        print("\n🎉 所有测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

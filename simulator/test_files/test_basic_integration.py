#!/usr/bin/env python3
"""
基本集成测试 - 验证高保真通信模拟组件的初始化
"""

import sys
import os
import logging

# 设置路径
sys.path.append(os.path.dirname(__file__))

def test_component_imports():
    """测试组件导入"""
    print("=== 测试组件导入 ===")
    
    try:
        from routing.topology_manager import TopologyManager
        print("✅ TopologyManager 导入成功")
        
        from routing.prediction_service import PredictionService  
        print("✅ PredictionService 导入成功")
        
        from routing.routing_optimizer import RoutingOptimizer
        print("✅ RoutingOptimizer 导入成功")
        
        from collective_communication.flow_generator import FlowGenerator
        print("✅ FlowGenerator 导入成功")
        
        from collective_communication.communication_simulator import CommunicationSimulator
        print("✅ CommunicationSimulator 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_component_initialization():
    """测试组件初始化"""
    print("\n=== 测试组件初始化 ===")
    
    try:
        from routing.topology_manager import TopologyManager
        from routing.prediction_service import PredictionService
        from routing.routing_optimizer import RoutingOptimizer
        from collective_communication.flow_generator import FlowGenerator
        from collective_communication.communication_simulator import CommunicationSimulator
        
        # 初始化组件
        print("初始化 TopologyManager...")
        topology_manager = TopologyManager()
        print("✅ TopologyManager 初始化成功")
        
        print("初始化 PredictionService...")
        prediction_service = PredictionService()
        print("✅ PredictionService 初始化成功")
        
        print("初始化 FlowGenerator...")
        flow_generator = FlowGenerator()
        print("✅ FlowGenerator 初始化成功")
        
        print("初始化 RoutingOptimizer...")
        routing_optimizer = RoutingOptimizer(prediction_service)
        print("✅ RoutingOptimizer 初始化成功")
        
        print("初始化 CommunicationSimulator...")
        communication_simulator = CommunicationSimulator(
            flow_generator, topology_manager, prediction_service
        )
        print("✅ CommunicationSimulator 初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simulator_integration():
    """测试与主模拟器的集成"""
    print("\n=== 测试主模拟器集成 ===")
    
    try:
        import pandas as pd
        from simulator import Cluster
        from pollux import PolluxPolicy
        
        # 创建简单的工作负载
        workload_data = {
            'name': ['test_job'],
            'application': ['cifar10'],
            'time': [0]
        }
        workload = pd.DataFrame(workload_data)
        
        # 创建集群
        policy = PolluxPolicy()
        cluster = Cluster(workload, policy, min_nodes=2, num_gpus=4)
        
        print(f"✅ 集群创建成功，高保真通信: {cluster.enable_high_fidelity_comm}")
        
        # 检查组件是否正确初始化
        if hasattr(cluster, 'topology_manager'):
            print("✅ TopologyManager 已集成")
        else:
            print("❌ TopologyManager 未集成")
            
        if hasattr(cluster, 'prediction_service'):
            print("✅ PredictionService 已集成")
        else:
            print("❌ PredictionService 未集成")
            
        if hasattr(cluster, 'communication_simulator'):
            print("✅ CommunicationSimulator 已集成")
        else:
            print("❌ CommunicationSimulator 未集成")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logging.basicConfig(level=logging.WARNING)  # 减少日志输出
    
    print("🚀 开始基本集成测试")
    
    success = True
    
    # 测试导入
    if not test_component_imports():
        success = False
    
    # 测试初始化  
    if not test_component_initialization():
        success = False
        
    # 测试集成
    if not test_simulator_integration():
        success = False
    
    if success:
        print("\n🎉 所有基本测试通过！高保真通信模拟系统已成功集成。")
    else:
        print("\n❌ 存在问题，请检查错误信息。")
    
    sys.exit(0 if success else 1) 
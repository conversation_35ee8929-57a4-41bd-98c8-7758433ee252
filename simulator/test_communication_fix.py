#!/usr/bin/env python3
"""
通信时间为0问题修复验证测试
验证修复后的通信模拟器能够正常工作
"""

import sys
import time
import logging
from typing import List, Dict

# 设置路径
sys.path.append('.')

from collective_communication.communication_simulator import CommunicationSimulator
from collective_communication.flow_generator import FlowGenerator
from routing.topology_manager import TopologyManager
from routing.prediction_service import PredictionService

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockJob:
    """模拟作业对象"""
    def __init__(self, name: str, num_replicas: int = 4):
        self.name = name
        self.num_replicas = num_replicas
        # 修复：placement应该是每个节点的GPU数量，而不是节点名称
        self.placement = tuple(1 for _ in range(num_replicas))  # 每个节点1个GPU
        self.atomic_bsz = 32
        self.accum_steps = 1
        self.profile = {
            'compute_time': 1.0,  # 1秒计算时间
            'sync_time': 0.1      # 0.1秒同步时间
        }

def create_test_flows(jobs: List[MockJob]) -> List[Dict]:
    """创建测试流数据"""
    flows = []
    
    for job in jobs:
        # 模拟Ring All-Reduce的轮数
        num_rounds = 2 * (job.num_replicas - 1)  # 标准Ring All-Reduce轮数
        
        for round_idx in range(num_rounds):
            for flow_idx in range(job.num_replicas):
                flow = {
                    'flow_id': f'{job.name}_round{round_idx}_flow{flow_idx}',
                    'job_name': job.name,
                    'round_idx': round_idx,
                    'status': 'PLANNED',
                    'start_time': 0.0,  # 将在模拟中设置
                    'flow_features': [10.0],  # 10MB数据
                    'path': ['H1', 'S1', 'P1', 'S2', 'H2'],
                    'model': 'test_model',
                    'dataset': 'test_dataset',
                    'parameters': 1000000.0,
                    'ringallreduce_group_size': job.num_replicas
                }
                flows.append(flow)
    
    return flows

def test_communication_fix():
    """测试通信时间修复"""
    print("=== 通信时间为0问题修复验证测试 ===")
    
    # 创建模拟器组件
    flow_generator = FlowGenerator()
    topology_manager = TopologyManager()
    prediction_service = PredictionService()
    
    comm_simulator = CommunicationSimulator(
        flow_generator, topology_manager, prediction_service
    )
    
    # 创建测试作业
    test_jobs = [
        MockJob("test_job_1", num_replicas=4),
        MockJob("test_job_2", num_replicas=3),
        MockJob("test_job_3", num_replicas=2)
    ]
    
    print(f"创建测试作业: {len(test_jobs)} 个")
    for job in test_jobs:
        print(f"  - {job.name}: {job.num_replicas} 个副本")
    
    # 创建测试流
    test_flows = create_test_flows(test_jobs)
    print(f"生成测试流: {len(test_flows)} 个")
    
    # 按作业统计流数量
    job_flow_counts = {}
    for flow in test_flows:
        job_name = flow['job_name']
        job_flow_counts[job_name] = job_flow_counts.get(job_name, 0) + 1
    
    for job_name, count in job_flow_counts.items():
        print(f"  - {job_name}: {count} 个流")
    
    # 设置流到通信模拟器
    print("\n--- 设置流数据 ---")
    comm_simulator.set_all_known_flows(test_flows)
    
    # 验证流数据设置成功
    flow_count_after_set = len(comm_simulator.all_known_flows)
    print(f"设置后流数量: {flow_count_after_set}")
    
    if flow_count_after_set != len(test_flows):
        print(f"❌ 流数据设置异常: 期望 {len(test_flows)}, 实际 {flow_count_after_set}")
        return False
    else:
        print("✅ 流数据设置成功")
    
    # 启用多次Ring All-Reduce模式
    comm_simulator.enable_multiple_allreduces = True
    comm_simulator.set_interval_start_time(0.0)
    
    # 执行通信模拟
    print("\n--- 执行通信模拟 ---")
    interval_duration = 60.0  # 60秒间隔
    
    try:
        start_time = time.time()
        communication_times = comm_simulator.run({}, test_jobs, interval_duration)
        simulation_time = time.time() - start_time
        
        print(f"模拟执行时间: {simulation_time:.4f}秒")
        print(f"通信时间结果: {communication_times}")
        
        # 验证结果
        success = True
        for job in test_jobs:
            comm_time = communication_times.get(job.name, 0.0)
            if comm_time > 0:
                print(f"✅ {job.name}: 通信时间 = {comm_time:.4f}秒")
            else:
                print(f"❌ {job.name}: 通信时间 = {comm_time:.4f}秒 (异常)")
                success = False
        
        # 检查Ring All-Reduce完成次数
        print("\n--- Ring All-Reduce完成统计 ---")
        for job in test_jobs:
            count = comm_simulator.allreduce_count.get(job.name, 0)
            print(f"{job.name}: 完成 {count} 次Ring All-Reduce")
        
        # 获取详细统计
        stats = comm_simulator.get_simulation_statistics()
        print(f"\n--- 模拟统计 ---")
        print(f"处理事件数: {stats['total_events_processed']}")
        print(f"模拟流数: {stats['total_flows_simulated']}")
        print(f"当前流数: {stats['total_known_flows']}")
        
        return success
        
    except Exception as e:
        print(f"❌ 模拟执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_initialization_order():
    """测试初始化顺序问题"""
    print("\n=== 初始化顺序测试 ===")
    
    # 创建模拟器
    flow_generator = FlowGenerator()
    topology_manager = TopologyManager()
    prediction_service = PredictionService()
    
    comm_simulator = CommunicationSimulator(
        flow_generator, topology_manager, prediction_service
    )
    
    # 创建简单测试数据
    test_job = MockJob("order_test_job", num_replicas=2)
    test_flows = create_test_flows([test_job])
    
    print(f"创建测试流: {len(test_flows)} 个")
    
    # 步骤1: 设置流
    comm_simulator.set_all_known_flows(test_flows)
    flows_after_set = len(comm_simulator.all_known_flows)
    print(f"设置流后数量: {flows_after_set}")
    
    # 步骤2: 模拟初始化（这里之前会清空流）
    comm_simulator._initialize_simulation([test_job], {})
    flows_after_init = len(comm_simulator.all_known_flows)
    print(f"初始化后数量: {flows_after_init}")
    
    # 验证流数据是否保持
    if flows_after_init == flows_after_set:
        print("✅ 初始化顺序修复成功：流数据得到保护")
        return True
    else:
        print(f"❌ 初始化顺序问题仍存在: {flows_after_set} -> {flows_after_init}")
        return False

def test_first_round_flow_detection():
    """测试第一轮流检测"""
    print("\n=== 第一轮流检测测试 ===")
    
    # 创建模拟器
    flow_generator = FlowGenerator()
    topology_manager = TopologyManager()
    prediction_service = PredictionService()
    
    comm_simulator = CommunicationSimulator(
        flow_generator, topology_manager, prediction_service
    )
    
    # 创建测试数据
    test_job = MockJob("detection_test_job", num_replicas=3)
    test_flows = create_test_flows([test_job])
    
    # 设置流
    comm_simulator.set_all_known_flows(test_flows)
    
    # 测试第一轮流检测
    first_round_flows = comm_simulator._get_job_first_round_flows(test_job)
    
    print(f"检测到第一轮流: {len(first_round_flows)} 个")
    
    # 验证结果
    expected_first_round_count = test_job.num_replicas  # 第一轮应该有num_replicas个流
    if len(first_round_flows) == expected_first_round_count:
        print(f"✅ 第一轮流检测正确: {len(first_round_flows)} 个流")
        return True
    else:
        print(f"❌ 第一轮流检测异常: 期望 {expected_first_round_count}, 实际 {len(first_round_flows)}")
        return False

if __name__ == "__main__":
    print("开始通信时间修复验证测试...\n")
    
    # 运行所有测试
    test_results = []
    
    test_results.append(("初始化顺序测试", test_initialization_order()))
    test_results.append(("第一轮流检测测试", test_first_round_flow_detection()))
    test_results.append(("通信时间修复测试", test_communication_fix()))
    
    # 总结结果
    print("\n" + "="*50)
    print("测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！通信时间为0问题已修复。")
    else:
        print("\n⚠️  部分测试失败，需要进一步调试。")

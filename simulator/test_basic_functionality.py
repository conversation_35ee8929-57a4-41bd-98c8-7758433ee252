#!/usr/bin/env python3
"""
基本功能测试 - 验证新增的方法是否正常工作
"""

import sys
import os

# 添加simulator目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_placement_detection():
    """测试placement变化检测功能"""
    print("=== 测试placement变化检测功能 ===")
    
    # 创建一个简单的模拟器类来测试方法
    class TestSimulator:
        def __init__(self):
            self.previous_placement = {}
        
        def _detect_placement_changes(self, current_placement: dict) -> dict:
            """检测placement变化，返回变化类型"""
            changes = {}
            
            # 检查当前作业的变化
            for job_name, current_alloc in current_placement.items():
                if job_name not in self.previous_placement:
                    changes[job_name] = 'new'
                elif not self._is_placement_identical(
                    self.previous_placement[job_name], current_alloc
                ):
                    changes[job_name] = 'modified'
                else:
                    changes[job_name] = 'no_change'
            
            # 检查被移除的作业
            for job_name in self.previous_placement:
                if job_name not in current_placement:
                    changes[job_name] = 'removed'
            
            return changes

        def _is_placement_identical(self, old_placement: list, new_placement: list) -> bool:
            """判断两个placement是否完全相同"""
            if old_placement is None or new_placement is None:
                return old_placement == new_placement
            
            return (len(old_placement) == len(new_placement) and 
                    all(a == b for a, b in zip(sorted(old_placement), sorted(new_placement))))
    
    # 创建测试实例
    simulator = TestSimulator()
    
    # 测试1: 相同placement
    old_placement = [0, 0, 1, 1]
    new_placement = [0, 0, 1, 1]
    result = simulator._is_placement_identical(old_placement, new_placement)
    print(f"相同placement检测: {result} (期望: True)")
    assert result == True, "相同placement检测失败"
    
    # 测试2: 不同placement
    old_placement = [0, 0, 1, 1]
    new_placement = [0, 1, 1, 1]
    result = simulator._is_placement_identical(old_placement, new_placement)
    print(f"不同placement检测: {result} (期望: False)")
    assert result == False, "不同placement检测失败"
    
    # 测试3: None情况
    result = simulator._is_placement_identical(None, None)
    print(f"None placement检测: {result} (期望: True)")
    assert result == True, "None placement检测失败"
    
    result = simulator._is_placement_identical([0, 1], None)
    print(f"一个None placement检测: {result} (期望: False)")
    assert result == False, "一个None placement检测失败"
    
    # 测试4: placement变化检测
    simulator.previous_placement = {
        'job1': [0, 0, 1, 1],
        'job2': [2, 2, 3, 3]
    }
    
    current_placement = {
        'job1': [0, 0, 1, 1],  # 无变化
        'job2': [0, 1, 2, 3],  # 修改
        'job3': [1, 1]         # 新增
    }
    
    changes = simulator._detect_placement_changes(current_placement)
    print(f"Placement变化检测结果: {changes}")
    
    expected_changes = {
        'job1': 'no_change',
        'job2': 'modified', 
        'job3': 'new'
    }
    
    for job_name, expected_change in expected_changes.items():
        assert changes[job_name] == expected_change, f"作业{job_name}的变化检测错误"
    
    print("✅ Placement变化检测功能测试通过")
    return True

def test_communication_simulator_methods():
    """测试通信模拟器的新方法"""
    print("\n=== 测试通信模拟器新方法 ===")
    
    # 创建一个简单的模拟器类来测试方法
    class TestCommunicationSimulator:
        def __init__(self):
            self.job_registry = {}
            self.tracker = TestTracker()
        
        def _save_cross_interval_state(self, interval_end_time: float):
            """保存跨间隔的状态信息"""
            partial_communication_times = {}
            
            for job_name in self.job_registry:
                # 获取作业在当前间隔内的部分通信时间
                job_start_time = None
                if (job_name in self.tracker.job_round_start_times and 
                    0 in self.tracker.job_round_start_times[job_name]):
                    job_start_time = self.tracker.job_round_start_times[job_name][0]
                
                if job_start_time is not None and job_start_time < interval_end_time:
                    # 计算到间隔结束时的部分通信时间
                    partial_time = interval_end_time - job_start_time
                    partial_communication_times[job_name] = partial_time
                else:
                    partial_communication_times[job_name] = 0.0
            
            # 将部分时间存储到tracker中
            for job_name, partial_time in partial_communication_times.items():
                if job_name in self.tracker.job_communication_times:
                    self.tracker.job_communication_times[job_name] = partial_time
            
            return partial_communication_times
    
    class TestTracker:
        def __init__(self):
            self.job_round_start_times = {
                'job1': {0: 10.0},
                'job2': {0: 20.0}
            }
            self.job_communication_times = {
                'job1': 0.0,
                'job2': 0.0
            }
    
    # 创建测试实例
    simulator = TestCommunicationSimulator()
    simulator.job_registry = {'job1': None, 'job2': None}
    
    # 测试保存跨间隔状态
    interval_end_time = 60.0
    partial_times = simulator._save_cross_interval_state(interval_end_time)
    
    print(f"部分通信时间: {partial_times}")
    
    expected_partial_times = {
        'job1': 50.0,  # 60.0 - 10.0
        'job2': 40.0   # 60.0 - 20.0
    }
    
    for job_name, expected_time in expected_partial_times.items():
        assert abs(partial_times[job_name] - expected_time) < 0.001, f"作业{job_name}的部分时间计算错误"
    
    print("✅ 通信模拟器新方法测试通过")
    return True

def main():
    """运行所有测试"""
    print("开始基本功能测试...")
    
    try:
        test_placement_detection()
        test_communication_simulator_methods()
        print("\n🎉 所有基本功能测试通过！")
        return True
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

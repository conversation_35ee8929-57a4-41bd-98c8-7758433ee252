#!/usr/bin/env python3
"""
批量预测系统测试套件
验证PredictionBuffer和批量预测功能的正确性
"""

import sys
import os
import time
import unittest
import logging

# 添加simulator目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from collective_communication.prediction_buffer import PredictionBuffer
from collective_communication.communication_simulator import CommunicationSimulator
from routing.prediction_service import PredictionService
from routing.topology_manager import TopologyManager
from collective_communication.flow_generator import FlowGenerator

# 设置日志级别
logging.basicConfig(level=logging.INFO)

class TestBatchPredictionSystem(unittest.TestCase):
    """批量预测系统测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.prediction_service = PredictionService()
        self.topology_manager = TopologyManager()
        self.flow_generator = FlowGenerator()
        
    def create_test_flow(self, flow_id: str, start_time: float = 0.0) -> dict:
        """创建测试流数据"""
        return {
            "flow_id": flow_id,
            "ringallreduce_group_size": 4,
            "flow_features": [10.0],
            "path": ["H1", "S1", "P1", "S2", "H2"],
            "start_time": start_time,
            "model": "cifar10",
            "dataset": "test",
            "parameters": 1000000.0,
            "status": "PLANNED",
            "job_name": "test_job",
            "round_idx": 0
        }
    
    def test_prediction_buffer_basic(self):
        """测试PredictionBuffer基本功能"""
        print("=== 测试PredictionBuffer基本功能 ===")
        
        buffer = PredictionBuffer(buffer_size_limit=3, time_window=0.1)
        
        # 测试初始状态
        self.assertEqual(buffer.get_pending_flow_count(), 0)
        self.assertFalse(buffer.should_flush_buffer(0.0))
        
        # 添加流
        flow1 = self.create_test_flow("test_flow_1", 0.0)
        buffer.add_flow_for_prediction(flow1, 0.0)
        self.assertEqual(buffer.get_pending_flow_count(), 1)
        
        # 测试统计信息
        stats = buffer.get_buffer_stats()
        self.assertEqual(stats['total_flows'], 1)
        self.assertEqual(stats['pending_flows'], 1)
        
        print("✅ PredictionBuffer基本功能测试通过")
    
    def test_batch_size_limits(self):
        """测试不同缓冲区大小限制的行为"""
        print("=== 测试批量大小限制 ===")
        
        test_cases = [1, 5, 10, 20]
        
        for limit in test_cases:
            buffer = PredictionBuffer(buffer_size_limit=limit, time_window=1.0)
            
            # 添加流直到达到限制
            for i in range(limit):
                flow = self.create_test_flow(f"flow_{i}", 0.0)
                buffer.add_flow_for_prediction(flow, 0.0)
                
                if i < limit - 1:
                    self.assertFalse(buffer.should_flush_buffer(0.0))
                else:
                    self.assertTrue(buffer.should_flush_buffer(0.0))
            
            print(f"✅ 批量大小限制 {limit} 测试通过")
    
    def test_time_window_flushing(self):
        """测试基于时间窗口的自动刷新"""
        print("=== 测试时间窗口刷新 ===")
        
        time_windows = [0.01, 0.05, 0.1]
        
        for window in time_windows:
            buffer = PredictionBuffer(buffer_size_limit=100, time_window=window)
            
            # 添加一个流
            flow = self.create_test_flow("test_flow", 0.0)
            buffer.add_flow_for_prediction(flow, 0.0)
            
            # 时间窗口内不应该刷新
            self.assertFalse(buffer.should_flush_buffer(window * 0.5))
            
            # 超过时间窗口应该刷新
            self.assertTrue(buffer.should_flush_buffer(window * 1.1))
            
            print(f"✅ 时间窗口 {window}s 测试通过")
    
    def test_integration_with_communication_simulator(self):
        """测试与CommunicationSimulator的集成"""
        print("=== 测试CommunicationSimulator集成 ===")
        
        # 创建CommunicationSimulator
        comm_sim = CommunicationSimulator(
            flow_generator=self.flow_generator,
            topology_manager=self.topology_manager,
            prediction_service=self.prediction_service,
            enable_routing_optimizer=False
        )
        
        # 验证批量预测已启用
        self.assertTrue(comm_sim.enable_batch_prediction)
        self.assertIsNotNone(comm_sim.prediction_buffer)
        
        # 测试预测缓冲区初始状态
        self.assertEqual(comm_sim.prediction_buffer.get_pending_flow_count(), 0)
        
        print("✅ CommunicationSimulator集成测试通过")
    
    def test_performance_comparison(self):
        """对比批量预测vs单流预测的性能"""
        print("=== 测试性能对比 ===")
        
        # 创建测试流
        num_flows = 50
        test_flows = []
        for i in range(num_flows):
            flow = self.create_test_flow(f"perf_test_flow_{i}", float(i))
            test_flows.append(flow)
        
        # 测试单流预测性能
        start_time = time.time()
        single_results = []
        for flow in test_flows:
            result = self.prediction_service.predict_fct([flow], test_flows)
            single_results.append(result[0] if result else 1.0)
        single_time = time.time() - start_time
        
        # 测试批量预测性能
        start_time = time.time()
        batch_results = self.prediction_service.predict_fct(test_flows, test_flows)
        batch_time = time.time() - start_time
        
        # 验证结果数量一致
        self.assertEqual(len(single_results), len(batch_results))
        
        # 计算性能提升
        if batch_time > 0:
            speedup = single_time / batch_time
            print(f"单流预测: {single_time:.4f}s")
            print(f"批量预测: {batch_time:.4f}s")
            print(f"性能提升: {speedup:.2f}x")
            
            # 批量预测应该更快（在合理范围内）
            self.assertGreater(speedup, 0.5)  # 至少不能太慢
        
        print("✅ 性能对比测试通过")
    
    def test_prediction_accuracy_consistency(self):
        """验证批量预测结果与单流预测结果的一致性"""
        print("=== 测试预测一致性 ===")
        
        # 创建少量测试流以便精确比较
        test_flows = []
        for i in range(5):
            flow = self.create_test_flow(f"consistency_flow_{i}", float(i * 10))
            test_flows.append(flow)
        
        # 单流预测结果
        single_results = []
        for flow in test_flows:
            result = self.prediction_service.predict_fct([flow], test_flows)
            single_results.append(result[0] if result else 1.0)
        
        # 批量预测结果
        batch_results = self.prediction_service.predict_fct(test_flows, test_flows)
        
        # 验证结果数量一致
        self.assertEqual(len(single_results), len(batch_results))
        
        # 验证结果值相近（考虑到浮点数精度）
        for i, (single, batch) in enumerate(zip(single_results, batch_results)):
            relative_error = abs(single - batch) / max(abs(single), abs(batch), 1e-6)
            self.assertLess(relative_error, 0.01, 
                          f"流 {i}: 单流预测={single:.6f}, 批量预测={batch:.6f}, 相对误差={relative_error:.6f}")
        
        print("✅ 预测一致性测试通过")

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始批量预测系统测试...")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestBatchPredictionSystem)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！批量预测系统工作正常。")
        return True
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1) 
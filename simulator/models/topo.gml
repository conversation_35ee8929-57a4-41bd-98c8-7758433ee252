graph [
  name "<PERSON><PERSON>(4,4,4)"
  node [
    id 0
    label "H1"
    type "host"
  ]
  node [
    id 1
    label "H2"
    type "host"
  ]
  node [
    id 2
    label "H3"
    type "host"
  ]
  node [
    id 3
    label "H4"
    type "host"
  ]
  node [
    id 4
    label "H5"
    type "host"
  ]
  node [
    id 5
    label "H6"
    type "host"
  ]
  node [
    id 6
    label "H7"
    type "host"
  ]
  node [
    id 7
    label "H8"
    type "host"
  ]
  node [
    id 8
    label "H9"
    type "host"
  ]
  node [
    id 9
    label "H10"
    type "host"
  ]
  node [
    id 10
    label "H11"
    type "host"
  ]
  node [
    id 11
    label "H12"
    type "host"
  ]
  node [
    id 12
    label "H13"
    type "host"
  ]
  node [
    id 13
    label "H14"
    type "host"
  ]
  node [
    id 14
    label "H15"
    type "host"
  ]
  node [
    id 15
    label "H16"
    type "host"
  ]
  node [
    id 16
    label "S1"
    type "switch"
  ]
  node [
    id 17
    label "S2"
    type "switch"
  ]
  node [
    id 18
    label "S3"
    type "switch"
  ]
  node [
    id 19
    label "S4"
    type "switch"
  ]
  node [
    id 20
    label "P1"
    type "switch"
  ]
  node [
    id 21
    label "P2"
    type "switch"
  ]
  node [
    id 22
    label "P3"
    type "switch"
  ]
  node [
    id 23
    label "P4"
    type "switch"
  ]
  node [
    id 24
    label "C1"
    type "switch"
  ]
  node [
    id 25
    label "C2"
    type "switch"
  ]
  node [
    id 26
    label "C3"
    type "switch"
  ]
  node [
    id 27
    label "C4"
    type "switch"
  ]
  edge [
    source 0
    target 16
  ]
  edge [
    source 1
    target 16
  ]
  edge [
    source 2
    target 16
  ]
  edge [
    source 3
    target 16
  ]
  edge [
    source 4
    target 17
  ]
  edge [
    source 5
    target 17
  ]
  edge [
    source 6
    target 17
  ]
  edge [
    source 7
    target 17
  ]
  edge [
    source 8
    target 18
  ]
  edge [
    source 9
    target 18
  ]
  edge [
    source 10
    target 18
  ]
  edge [
    source 11
    target 18
  ]
  edge [
    source 12
    target 19
  ]
  edge [
    source 13
    target 19
  ]
  edge [
    source 14
    target 19
  ]
  edge [
    source 15
    target 19
  ]
  edge [
    source 16
    target 20
  ]
  edge [
    source 16
    target 21
  ]
  edge [
    source 16
    target 22
  ]
  edge [
    source 16
    target 23
  ]
  edge [
    source 17
    target 20
  ]
  edge [
    source 17
    target 21
  ]
  edge [
    source 17
    target 22
  ]
  edge [
    source 17
    target 23
  ]
  edge [
    source 18
    target 20
  ]
  edge [
    source 18
    target 21
  ]
  edge [
    source 18
    target 22
  ]
  edge [
    source 18
    target 23
  ]
  edge [
    source 19
    target 20
  ]
  edge [
    source 19
    target 21
  ]
  edge [
    source 19
    target 22
  ]
  edge [
    source 19
    target 23
  ]
  edge [
    source 20
    target 24
  ]
  edge [
    source 20
    target 25
  ]
  edge [
    source 20
    target 26
  ]
  edge [
    source 20
    target 27
  ]
  edge [
    source 21
    target 24
  ]
  edge [
    source 21
    target 25
  ]
  edge [
    source 21
    target 26
  ]
  edge [
    source 21
    target 27
  ]
  edge [
    source 22
    target 24
  ]
  edge [
    source 22
    target 25
  ]
  edge [
    source 22
    target 26
  ]
  edge [
    source 22
    target 27
  ]
  edge [
    source 23
    target 24
  ]
  edge [
    source 23
    target 25
  ]
  edge [
    source 23
    target 26
  ]
  edge [
    source 23
    target 27
  ]
]

graph [
  directed 1
  node [
    id 0
    label "P1"
    idx 1
    type "[1, 0, 0]"
  ]
  node [
    id 1
    label "P2"
    idx 2
    type "[1, 0, 0]"
  ]
  node [
    id 2
    label "P3"
    idx 3
    type "[1, 0, 0]"
  ]
  node [
    id 3
    label "P4"
    idx 4
    type "[1, 0, 0]"
  ]
  node [
    id 4
    label "S1"
    idx 5
    type "[0, 1, 0]"
  ]
  node [
    id 5
    label "S2"
    idx 6
    type "[0, 1, 0]"
  ]
  node [
    id 6
    label "S3"
    idx 7
    type "[0, 1, 0]"
  ]
  node [
    id 7
    label "S4"
    idx 8
    type "[0, 1, 0]"
  ]
  node [
    id 8
    label "H1"
    idx 9
    type "[0, 0, 1]"
  ]
  node [
    id 9
    label "H2"
    idx 10
    type "[0, 0, 1]"
  ]
  node [
    id 10
    label "H3"
    idx 11
    type "[0, 0, 1]"
  ]
  node [
    id 11
    label "H4"
    idx 12
    type "[0, 0, 1]"
  ]
  node [
    id 12
    label "H5"
    idx 13
    type "[0, 0, 1]"
  ]
  node [
    id 13
    label "H6"
    idx 14
    type "[0, 0, 1]"
  ]
  node [
    id 14
    label "H7"
    idx 15
    type "[0, 0, 1]"
  ]
  node [
    id 15
    label "H8"
    idx 16
    type "[0, 0, 1]"
  ]
  node [
    id 16
    label "H9"
    idx 17
    type "[0, 0, 1]"
  ]
  node [
    id 17
    label "H10"
    idx 18
    type "[0, 0, 1]"
  ]
  node [
    id 18
    label "H11"
    idx 19
    type "[0, 0, 1]"
  ]
  node [
    id 19
    label "H12"
    idx 20
    type "[0, 0, 1]"
  ]
  node [
    id 20
    label "H13"
    idx 21
    type "[0, 0, 1]"
  ]
  node [
    id 21
    label "H14"
    idx 22
    type "[0, 0, 1]"
  ]
  node [
    id 22
    label "H15"
    idx 23
    type "[0, 0, 1]"
  ]
  node [
    id 23
    label "H16"
    idx 24
    type "[0, 0, 1]"
  ]
  edge [
    source 0
    target 4
    idx 1
    btw 400
    type "PS"
  ]
  edge [
    source 0
    target 5
    idx 3
    btw 400
    type "PS"
  ]
  edge [
    source 0
    target 6
    idx 5
    btw 400
    type "PS"
  ]
  edge [
    source 0
    target 7
    idx 7
    btw 400
    type "PS"
  ]
  edge [
    source 1
    target 4
    idx 9
    btw 400
    type "PS"
  ]
  edge [
    source 1
    target 5
    idx 11
    btw 400
    type "PS"
  ]
  edge [
    source 1
    target 6
    idx 13
    btw 400
    type "PS"
  ]
  edge [
    source 1
    target 7
    idx 15
    btw 400
    type "PS"
  ]
  edge [
    source 2
    target 4
    idx 17
    btw 400
    type "PS"
  ]
  edge [
    source 2
    target 5
    idx 19
    btw 400
    type "PS"
  ]
  edge [
    source 2
    target 6
    idx 21
    btw 400
    type "PS"
  ]
  edge [
    source 2
    target 7
    idx 23
    btw 400
    type "PS"
  ]
  edge [
    source 3
    target 4
    idx 25
    btw 400
    type "PS"
  ]
  edge [
    source 3
    target 5
    idx 27
    btw 400
    type "PS"
  ]
  edge [
    source 3
    target 6
    idx 29
    btw 400
    type "PS"
  ]
  edge [
    source 3
    target 7
    idx 31
    btw 400
    type "PS"
  ]
  edge [
    source 4
    target 0
    idx 2
    btw 400
    type "SP"
  ]
  edge [
    source 4
    target 1
    idx 10
    btw 400
    type "SP"
  ]
  edge [
    source 4
    target 2
    idx 18
    btw 400
    type "SP"
  ]
  edge [
    source 4
    target 3
    idx 26
    btw 400
    type "SP"
  ]
  edge [
    source 4
    target 8
    idx 33
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 9
    idx 35
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 10
    idx 37
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 11
    idx 39
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 12
    idx 41
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 13
    idx 43
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 14
    idx 45
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 15
    idx 47
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 16
    idx 49
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 17
    idx 51
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 18
    idx 53
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 19
    idx 55
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 20
    idx 57
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 21
    idx 59
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 22
    idx 61
    btw 400
    type "SH"
  ]
  edge [
    source 4
    target 23
    idx 63
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 0
    idx 4
    btw 400
    type "SP"
  ]
  edge [
    source 5
    target 1
    idx 12
    btw 400
    type "SP"
  ]
  edge [
    source 5
    target 2
    idx 20
    btw 400
    type "SP"
  ]
  edge [
    source 5
    target 3
    idx 28
    btw 400
    type "SP"
  ]
  edge [
    source 5
    target 8
    idx 65
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 9
    idx 67
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 10
    idx 69
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 11
    idx 71
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 12
    idx 73
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 13
    idx 75
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 14
    idx 77
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 15
    idx 79
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 16
    idx 81
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 17
    idx 83
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 18
    idx 85
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 19
    idx 87
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 20
    idx 89
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 21
    idx 91
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 22
    idx 93
    btw 400
    type "SH"
  ]
  edge [
    source 5
    target 23
    idx 95
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 0
    idx 6
    btw 400
    type "SP"
  ]
  edge [
    source 6
    target 1
    idx 14
    btw 400
    type "SP"
  ]
  edge [
    source 6
    target 2
    idx 22
    btw 400
    type "SP"
  ]
  edge [
    source 6
    target 3
    idx 30
    btw 400
    type "SP"
  ]
  edge [
    source 6
    target 8
    idx 97
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 9
    idx 99
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 10
    idx 101
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 11
    idx 103
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 12
    idx 105
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 13
    idx 107
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 14
    idx 109
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 15
    idx 111
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 16
    idx 113
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 17
    idx 115
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 18
    idx 117
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 19
    idx 119
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 20
    idx 121
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 21
    idx 123
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 22
    idx 125
    btw 400
    type "SH"
  ]
  edge [
    source 6
    target 23
    idx 127
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 0
    idx 8
    btw 400
    type "SP"
  ]
  edge [
    source 7
    target 1
    idx 16
    btw 400
    type "SP"
  ]
  edge [
    source 7
    target 2
    idx 24
    btw 400
    type "SP"
  ]
  edge [
    source 7
    target 3
    idx 32
    btw 400
    type "SP"
  ]
  edge [
    source 7
    target 8
    idx 129
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 9
    idx 131
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 10
    idx 133
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 11
    idx 135
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 12
    idx 137
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 13
    idx 139
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 14
    idx 141
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 15
    idx 143
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 16
    idx 145
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 17
    idx 147
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 18
    idx 149
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 19
    idx 151
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 20
    idx 153
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 21
    idx 155
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 22
    idx 157
    btw 400
    type "SH"
  ]
  edge [
    source 7
    target 23
    idx 159
    btw 400
    type "SH"
  ]
  edge [
    source 8
    target 4
    idx 34
    btw 400
    type "HS"
  ]
  edge [
    source 8
    target 5
    idx 66
    btw 400
    type "HS"
  ]
  edge [
    source 8
    target 6
    idx 98
    btw 400
    type "HS"
  ]
  edge [
    source 8
    target 7
    idx 130
    btw 400
    type "HS"
  ]
  edge [
    source 9
    target 4
    idx 36
    btw 400
    type "HS"
  ]
  edge [
    source 9
    target 5
    idx 68
    btw 400
    type "HS"
  ]
  edge [
    source 9
    target 6
    idx 100
    btw 400
    type "HS"
  ]
  edge [
    source 9
    target 7
    idx 132
    btw 400
    type "HS"
  ]
  edge [
    source 10
    target 4
    idx 38
    btw 400
    type "HS"
  ]
  edge [
    source 10
    target 5
    idx 70
    btw 400
    type "HS"
  ]
  edge [
    source 10
    target 6
    idx 102
    btw 400
    type "HS"
  ]
  edge [
    source 10
    target 7
    idx 134
    btw 400
    type "HS"
  ]
  edge [
    source 11
    target 4
    idx 40
    btw 400
    type "HS"
  ]
  edge [
    source 11
    target 5
    idx 72
    btw 400
    type "HS"
  ]
  edge [
    source 11
    target 6
    idx 104
    btw 400
    type "HS"
  ]
  edge [
    source 11
    target 7
    idx 136
    btw 400
    type "HS"
  ]
  edge [
    source 12
    target 4
    idx 42
    btw 400
    type "HS"
  ]
  edge [
    source 12
    target 5
    idx 74
    btw 400
    type "HS"
  ]
  edge [
    source 12
    target 6
    idx 106
    btw 400
    type "HS"
  ]
  edge [
    source 12
    target 7
    idx 138
    btw 400
    type "HS"
  ]
  edge [
    source 13
    target 4
    idx 44
    btw 400
    type "HS"
  ]
  edge [
    source 13
    target 5
    idx 76
    btw 400
    type "HS"
  ]
  edge [
    source 13
    target 6
    idx 108
    btw 400
    type "HS"
  ]
  edge [
    source 13
    target 7
    idx 140
    btw 400
    type "HS"
  ]
  edge [
    source 14
    target 4
    idx 46
    btw 400
    type "HS"
  ]
  edge [
    source 14
    target 5
    idx 78
    btw 400
    type "HS"
  ]
  edge [
    source 14
    target 6
    idx 110
    btw 400
    type "HS"
  ]
  edge [
    source 14
    target 7
    idx 142
    btw 400
    type "HS"
  ]
  edge [
    source 15
    target 4
    idx 48
    btw 400
    type "HS"
  ]
  edge [
    source 15
    target 5
    idx 80
    btw 400
    type "HS"
  ]
  edge [
    source 15
    target 6
    idx 112
    btw 400
    type "HS"
  ]
  edge [
    source 15
    target 7
    idx 144
    btw 400
    type "HS"
  ]
  edge [
    source 16
    target 4
    idx 50
    btw 400
    type "HS"
  ]
  edge [
    source 16
    target 5
    idx 82
    btw 400
    type "HS"
  ]
  edge [
    source 16
    target 6
    idx 114
    btw 400
    type "HS"
  ]
  edge [
    source 16
    target 7
    idx 146
    btw 400
    type "HS"
  ]
  edge [
    source 17
    target 4
    idx 52
    btw 400
    type "HS"
  ]
  edge [
    source 17
    target 5
    idx 84
    btw 400
    type "HS"
  ]
  edge [
    source 17
    target 6
    idx 116
    btw 400
    type "HS"
  ]
  edge [
    source 17
    target 7
    idx 148
    btw 400
    type "HS"
  ]
  edge [
    source 18
    target 4
    idx 54
    btw 400
    type "HS"
  ]
  edge [
    source 18
    target 5
    idx 86
    btw 400
    type "HS"
  ]
  edge [
    source 18
    target 6
    idx 118
    btw 400
    type "HS"
  ]
  edge [
    source 18
    target 7
    idx 150
    btw 400
    type "HS"
  ]
  edge [
    source 19
    target 4
    idx 56
    btw 400
    type "HS"
  ]
  edge [
    source 19
    target 5
    idx 88
    btw 400
    type "HS"
  ]
  edge [
    source 19
    target 6
    idx 120
    btw 400
    type "HS"
  ]
  edge [
    source 19
    target 7
    idx 152
    btw 400
    type "HS"
  ]
  edge [
    source 20
    target 4
    idx 58
    btw 400
    type "HS"
  ]
  edge [
    source 20
    target 5
    idx 90
    btw 400
    type "HS"
  ]
  edge [
    source 20
    target 6
    idx 122
    btw 400
    type "HS"
  ]
  edge [
    source 20
    target 7
    idx 154
    btw 400
    type "HS"
  ]
  edge [
    source 21
    target 4
    idx 60
    btw 400
    type "HS"
  ]
  edge [
    source 21
    target 5
    idx 92
    btw 400
    type "HS"
  ]
  edge [
    source 21
    target 6
    idx 124
    btw 400
    type "HS"
  ]
  edge [
    source 21
    target 7
    idx 156
    btw 400
    type "HS"
  ]
  edge [
    source 22
    target 4
    idx 62
    btw 400
    type "HS"
  ]
  edge [
    source 22
    target 5
    idx 94
    btw 400
    type "HS"
  ]
  edge [
    source 22
    target 6
    idx 126
    btw 400
    type "HS"
  ]
  edge [
    source 22
    target 7
    idx 158
    btw 400
    type "HS"
  ]
  edge [
    source 23
    target 4
    idx 64
    btw 400
    type "HS"
  ]
  edge [
    source 23
    target 5
    idx 96
    btw 400
    type "HS"
  ]
  edge [
    source 23
    target 6
    idx 128
    btw 400
    type "HS"
  ]
  edge [
    source 23
    target 7
    idx 160
    btw 400
    type "HS"
  ]
]

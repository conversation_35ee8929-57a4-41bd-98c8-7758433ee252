#!/usr/bin/env python3
"""
拓扑管理器 - 管理网络拓扑、提供ECMP路径查找
严格实现固定4跳路径：H-S-P-S-H
"""

import os
import networkx as nx
from typing import List, Dict, Tuple, Optional
from collections import defaultdict
import logging
from .ecmp_calculator_4hop import ECMPCalculator4Hop

logger = logging.getLogger(__name__)

class TopologyManager:
    """网络拓扑管理器，提供ECMP路径查找和缓存功能"""
    
    def __init__(self, topo_path: str = None):
        """
        初始化拓扑管理器
        
        Args:
            topo_path: topo.gml文件路径，默认为simulator/routing/topo.gml
        """
        if topo_path is None:
            # 默认路径
            current_dir = os.path.dirname(__file__)
            topo_path = os.path.join(current_dir, "topo.gml")
        
        self.topo_path = topo_path
        self.G = None
        self.hosts = []
        self.switches = []
        self.pod_switches = []
        
        # ECMP路径缓存：{(src, dst): [path1, path2, ...]}
        self.path_cache = {}
        
        # 初始化专用的4跳ECMP计算器
        self.ecmp_calculator = ECMPCalculator4Hop(topo_path)
        
        # 加载拓扑
        self._load_topology()
        self._categorize_nodes()
        
        logger.info(f"拓扑管理器初始化完成: {len(self.hosts)}个主机, {len(self.switches)}个交换机, {len(self.pod_switches)}个POD交换机")
    
    def _load_topology(self):
        """加载topo.gml文件"""
        try:
            self.G = nx.read_gml(self.topo_path, label='label')
            logger.info(f"成功加载拓扑文件: {self.topo_path}")
            logger.info(f"拓扑包含 {self.G.number_of_nodes()} 个节点, {self.G.number_of_edges()} 条边")
        except Exception as e:
            raise RuntimeError(f"无法加载拓扑文件 {self.topo_path}: {e}")
    
    def _categorize_nodes(self):
        """将节点按类型分类"""
        for node in self.G.nodes():
            if node.startswith('H'):
                self.hosts.append(node)
            elif node.startswith('S'):
                self.switches.append(node)
            elif node.startswith('P'):
                self.pod_switches.append(node)
        
        # 排序以确保一致性
        self.hosts.sort(key=lambda x: int(x[1:]))
        self.switches.sort(key=lambda x: int(x[1:]))
        self.pod_switches.sort(key=lambda x: int(x[1:]))
    
    def get_ecmp_paths(self, src: str, dst: str) -> List[List[str]]:
        """
        获取源和目的主机之间的所有ECMP路径
        严格使用4跳路径：H-S-P-S-H
        
        Args:
            src: 源主机，如'H1'
            dst: 目的主机，如'H5'
            
        Returns:
            路径列表，每个路径为节点名称列表
        """
        # 检查输入有效性
        if not src.startswith('H') or not dst.startswith('H'):
            raise ValueError(f"源和目的必须是主机节点，收到: {src}, {dst}")
        
        if src == dst:
            return [[src]]
        
        # 检查缓存
        cache_key = (src, dst)
        if cache_key in self.path_cache:
            return self.path_cache[cache_key]
        
        # 使用4跳ECMP计算器计算路径
        paths = self._compute_4hop_ecmp_paths(src, dst)
        
        # 缓存结果
        self.path_cache[cache_key] = paths
        
        logger.debug(f"找到 {len(paths)} 条从 {src} 到 {dst} 的4跳ECMP路径")
        return paths
    
    def _compute_4hop_ecmp_paths(self, src: str, dst: str) -> List[List[str]]:
        """
        使用专用的4跳ECMP计算器计算路径
        
        Args:
            src: 源主机名，如'H1'
            dst: 目的主机名，如'H5'
            
        Returns:
            4跳路径列表，每个路径为节点名称列表
        """
        try:
            # 将主机名转换为索引（H1->0, H2->1, ...）
            src_index = self._host_name_to_index(src)
            dst_index = self._host_name_to_index(dst)
            
            # 使用ECMPCalculator4Hop计算所有4跳ECMP路径
            paths = self.ecmp_calculator.calculate_ecmp_for_flow_4hop(src_index, dst_index)
            
            if paths:
                return paths
            else:
                logger.warning(f"ECMPCalculator4Hop未找到从 {src} 到 {dst} 的4跳路径")
                return []
                
        except Exception as e:
            logger.error(f"计算4跳路径时出错: {e}")
            return []
    
    def _host_name_to_index(self, host_name: str) -> int:
        """
        将主机名转换为索引
        
        Args:
            host_name: 主机名，如'H1', 'H2'
            
        Returns:
            主机索引（0-based）
        """
        if not host_name.startswith('H'):
            raise ValueError(f"无效的主机名: {host_name}")
        
        try:
            # H1->0, H2->1, H3->2, ...
            return int(host_name[1:]) - 1
        except ValueError:
            raise ValueError(f"无法解析主机名: {host_name}")
    
    def _is_valid_4hop_path(self, path: List[str]) -> bool:
        """
        验证路径是否符合4跳模式：H-S-P-S-H
        
        Args:
            path: 路径节点列表
            
        Returns:
            是否为有效的4跳路径
        """
        if len(path) != 5:
            return False
        
        # 检查模式：H-S-P-S-H
        return (path[0].startswith('H') and 
                path[1].startswith('S') and
                path[2].startswith('P') and
                path[3].startswith('S') and
                path[4].startswith('H'))
    
    def get_hosts(self) -> List[str]:
        """获取所有主机节点"""
        return self.hosts.copy()
    
    def get_switches(self) -> List[str]:
        """获取所有交换机节点"""
        return self.switches.copy()
    
    def get_pod_switches(self) -> List[str]:
        """获取所有POD交换机节点"""
        return self.pod_switches.copy()
    
    def get_link_bandwidth(self, src: str, dst: str) -> float:
        """
        获取链路带宽
        
        Args:
            src: 源节点
            dst: 目的节点
            
        Returns:
            带宽值（Gbps）
        """
        if self.G.has_edge(src, dst):
            edge_data = self.G.edges[src, dst]
            return edge_data.get('btw', 400.0)  # 默认400Gbps
        else:
            logger.warning(f"链路 {src}->{dst} 不存在")
            return 0.0
    
    def validate_path(self, path: List[str]) -> bool:
        """
        验证路径是否在拓扑中存在
        
        Args:
            path: 路径节点列表
            
        Returns:
            路径是否有效
        """
        if len(path) < 2:
            return True
        
        for i in range(len(path) - 1):
            if not self.G.has_edge(path[i], path[i + 1]):
                return False
        
        return True
    
    def get_network_graph(self) -> nx.Graph:
        """获取网络拓扑图（只读）"""
        return self.G.copy()
    
    def clear_cache(self):
        """清空路径缓存"""
        self.path_cache.clear()
        logger.info("路径缓存已清空")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计信息"""
        return {
            "cached_paths": len(self.path_cache),
            "total_hosts": len(self.hosts),
            "max_possible_pairs": len(self.hosts) * (len(self.hosts) - 1)
        } 
#!/usr/bin/env python3
"""
路由优化器 - 实现两阶段启发式路由优化算法
严格按照optimization_problem.md中的公式16、17、19实现
"""

import logging
import random
import copy
from typing import List, Dict, Any, Tuple, Set
from collections import defaultdict
import numpy as np

logger = logging.getLogger(__name__)

class RoutingOptimizer:
    """两阶段启发式路由优化器"""
    
    def __init__(self, prediction_service, max_iterations: int = 50, 
                 k_candidate_paths: int = 3, omega1: float = 2.0, omega2: float = 1.0):
        """
        初始化路由优化器
        
        Args:
            prediction_service: 预测服务实例
            max_iterations: 最大迭代次数
            k_candidate_paths: 每个冲突流保留的候选路径数量
            omega1: 当前调度流路径重叠权重
            omega2: 未来流路径共享权重
        """
        self.prediction_service = prediction_service
        self.max_iterations = max_iterations
        self.k_candidate_paths = k_candidate_paths
        self.omega1 = omega1
        self.omega2 = omega2
        
        # 算法状态
        self.current_time = 0.0
        self.decision_window = 1.0  # 决策窗口时间（秒）
        self.modification_flags = {}  # 流修改标志 {flow_id: modified}
        
        logger.info("路由优化器初始化完成")
    
    def solve(self, all_potential_flows: List[Dict]) -> Dict[str, List[str]]:
        """
        求解路由优化问题，返回最优路由方案
        
        Args:
            all_potential_flows: 所有潜在流列表
            
        Returns:
            最优路由方案 {flow_id: chosen_optimal_path}
        """
        if not all_potential_flows:
            return {}
        
        logger.debug(f"开始路由优化，输入 {len(all_potential_flows)} 个流")
        
        # 初始化路由方案（使用默认路径）
        routing_plan = self._initialize_routing_plan(all_potential_flows)
        
        # 分类流：调度流、背景流、未来流
        scheduled_flows, background_flows, future_flows = self._classify_flows(all_potential_flows)
        
        logger.debug(f"流分类: 调度流={len(scheduled_flows)}, 背景流={len(background_flows)}, 未来流={len(future_flows)}")
        
        # 阶段1: 识别冲突流并筛选候选路径
        conflict_flows = self._identify_conflict_flows(scheduled_flows, future_flows)
        logger.info(f"识别出 {len(conflict_flows)} 个冲突流")
        
        if not conflict_flows:
            logger.info("无冲突流，使用默认路由方案")
            return routing_plan
        
        # 为冲突流生成候选路径
        candidate_paths_map = self._generate_candidate_paths(conflict_flows, scheduled_flows, future_flows)
        
        # 阶段2: 迭代优化求解
        optimal_routing = self._iterative_optimization(
            conflict_flows, candidate_paths_map, scheduled_flows, 
            all_potential_flows, routing_plan
        )
        
        # 更新路由方案
        routing_plan.update(optimal_routing)
        
        logger.info("路由优化完成")
        return routing_plan
    
    def _initialize_routing_plan(self, flows: List[Dict]) -> Dict[str, List[str]]:
        """
        初始化路由方案，使用每个流的默认路径（第一条候选路径）
        
        Args:
            flows: 流列表
            
        Returns:
            初始路由方案
        """
        routing_plan = {}
        
        for flow in flows:
            flow_id = flow["flow_id"]
            candidate_paths = flow.get("candidate_paths", [])
            
            if candidate_paths:
                # 使用第一条路径作为默认路径
                routing_plan[flow_id] = candidate_paths[0]
            else:
                # 如果没有候选路径，使用path字段
                routing_plan[flow_id] = flow.get("path", [])
        
        return routing_plan
    
    def _classify_flows(self, flows: List[Dict]) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """
        将流分类为调度流、背景流和未来流
        
        Args:
            flows: 所有流列表
            
        Returns:
            (调度流, 背景流, 未来流)
        """
        scheduled_flows = []  # S(t): 当前时刻需要调度的流
        background_flows = []  # B(t): 当前正在传输的背景流
        future_flows = []     # F(t, Δt): 在决策窗口内的未来流
        
        current_time = self.current_time
        decision_end_time = current_time + self.decision_window
        
        for flow in flows:
            start_time = flow.get("start_time", 0.0)
            status = flow.get("status", "PLANNED")
            
            if status == "ACTIVE" or start_time <= current_time:
                if status == "ACTIVE":
                    background_flows.append(flow)
                else:
                    scheduled_flows.append(flow)
            elif current_time < start_time <= decision_end_time:
                future_flows.append(flow)
        
        return scheduled_flows, background_flows, future_flows
    
    def _identify_conflict_flows(self, scheduled_flows: List[Dict], 
                               future_flows: List[Dict]) -> List[Dict]:
        """
        识别冲突流 - 实现公式(17)
        
        Args:
            scheduled_flows: 调度流
            future_flows: 未来流
            
        Returns:
            冲突流列表
        """
        conflict_flows = []
        
        # 收集当前调度流的默认路径上的所有链路
        scheduled_links = set()
        for flow in scheduled_flows:
            path = flow.get("path", [])
            if len(path) > 1:
                for i in range(len(path) - 1):
                    link = (path[i], path[i + 1])
                    scheduled_links.add(link)
        
        # 检查每个未来流是否与调度流路径重叠
        for flow in future_flows:
            flow_id = flow["flow_id"]
            
            # 检查修改标志 μ(f) = 0 (未被修改过)
            if self.modification_flags.get(flow_id, False):
                continue
            
            # 获取该流的默认路径（ECMP路径）
            default_path = self._get_default_path(flow)
            
            # 检查路径重叠
            if self._has_path_overlap(default_path, scheduled_links):
                conflict_flows.append(flow)
        
        return conflict_flows
    
    def _get_default_path(self, flow: Dict) -> List[str]:
        """
        获取流的默认路径（ECMP路径）
        
        Args:
            flow: 流数据
            
        Returns:
            默认路径
        """
        candidate_paths = flow.get("candidate_paths", [])
        if candidate_paths:
            return candidate_paths[0]  # 第一条候选路径作为默认路径
        else:
            return flow.get("path", [])
    
    def _has_path_overlap(self, path: List[str], scheduled_links: Set[Tuple[str, str]]) -> bool:
        """
        检查路径是否与调度流链路重叠
        
        Args:
            path: 检查的路径
            scheduled_links: 调度流占用的链路集合
            
        Returns:
            是否有重叠
        """
        if len(path) < 2:
            return False
        
        for i in range(len(path) - 1):
            link = (path[i], path[i + 1])
            if link in scheduled_links:
                return True
        
        return False
    
    def _generate_candidate_paths(self, conflict_flows: List[Dict], 
                                scheduled_flows: List[Dict], 
                                future_flows: List[Dict]) -> Dict[str, List[List[str]]]:
        """
        为冲突流生成候选路径 - 实现公式(19)的代价函数
        
        Args:
            conflict_flows: 冲突流
            scheduled_flows: 调度流  
            future_flows: 未来流
            
        Returns:
            候选路径映射 {flow_id: [path1, path2, ...]}
        """
        candidate_paths_map = {}
        
        # 预计算共享链路使用情况
        link_usage = self._compute_link_usage(future_flows)
        
        # 收集调度流占用的链路
        scheduled_links = set()
        for flow in scheduled_flows:
            path = flow.get("path", [])
            if len(path) > 1:
                for i in range(len(path) - 1):
                    link = (path[i], path[i + 1])
                    scheduled_links.add(link)
        
        for flow in conflict_flows:
            flow_id = flow["flow_id"]
            all_paths = flow.get("candidate_paths", [])
            
            if not all_paths:
                candidate_paths_map[flow_id] = []
                continue
            
            # 为每条路径计算代价 ψ(p, t)
            path_costs = []
            for path in all_paths:
                cost = self._calculate_path_cost(path, scheduled_links, link_usage)
                path_costs.append((cost, path))
            
            # 按代价排序，选择K条最优路径
            path_costs.sort(key=lambda x: x[0])
            selected_paths = [path for cost, path in path_costs[:self.k_candidate_paths]]
            
            candidate_paths_map[flow_id] = selected_paths
        
        return candidate_paths_map
    
    def _calculate_path_cost(self, path: List[str], scheduled_links: Set[Tuple[str, str]], 
                           link_usage: Dict[Tuple[str, str], int]) -> float:
        """
        计算路径代价 - 实现公式(19)
        
        Args:
            path: 路径
            scheduled_links: 调度流占用的链路
            link_usage: 链路使用情况
            
        Returns:
            路径代价
        """
        if len(path) < 2:
            return 0.0
        
        total_cost = 0.0
        
        for i in range(len(path) - 1):
            link = (path[i], path[i + 1])
            
            # ω1 * I(e ∈ ∪s∈S(t) p^(0)(s))
            overlap_penalty = self.omega1 if link in scheduled_links else 0.0
            
            # ω2 * ν_e(t)
            usage_penalty = self.omega2 * link_usage.get(link, 0)
            
            total_cost += overlap_penalty + usage_penalty
        
        return total_cost
    
    def _compute_link_usage(self, future_flows: List[Dict]) -> Dict[Tuple[str, str], int]:
        """
        计算链路在未来流中的使用情况
        
        Args:
            future_flows: 未来流列表
            
        Returns:
            链路使用计数 {(src, dst): count}
        """
        link_usage = defaultdict(int)
        
        for flow in future_flows:
            path = self._get_default_path(flow)
            if len(path) > 1:
                for i in range(len(path) - 1):
                    link = (path[i], path[i + 1])
                    link_usage[link] += 1
        
        return dict(link_usage)
    
    def _iterative_optimization(self, conflict_flows: List[Dict], 
                              candidate_paths_map: Dict[str, List[List[str]]],
                              scheduled_flows: List[Dict],
                              all_flows: List[Dict],
                              current_routing: Dict[str, List[str]]) -> Dict[str, List[str]]:
        """
        迭代优化求解 - 实现公式(16)的目标函数
        
        Args:
            conflict_flows: 冲突流
            candidate_paths_map: 候选路径映射
            scheduled_flows: 调度流
            all_flows: 所有流
            current_routing: 当前路由方案
            
        Returns:
            优化后的路由方案
        """
        best_routing = {}
        best_objective = float('inf')
        
        # 优化：实现局部搜索（Local Search）算法，而不是纯随机搜索
        
        # 1. 生成初始解（贪心策略）
        initial_solution = {}
        for flow in conflict_flows:
            flow_id = flow["flow_id"]
            candidate_paths = candidate_paths_map.get(flow_id, [])
            if candidate_paths:
                initial_solution[flow_id] = candidate_paths[0]  # 代价最低的路径
        
        # 2. 评估初始解
        best_objective = self._evaluate_objective(
            initial_solution, scheduled_flows, all_flows, current_routing
        )
        best_routing = initial_solution.copy()
        logger.debug(f"初始解目标值: {best_objective:.4f}")
        
        # 3. 迭代改进
        for iteration in range(self.max_iterations):
            if not conflict_flows:
                break
            
            # 随机选择一个冲突流进行变异
            flow_to_modify = random.choice(conflict_flows)
            flow_id_to_modify = flow_to_modify["flow_id"]
            
            candidate_paths = candidate_paths_map.get(flow_id_to_modify, [])
            if len(candidate_paths) < 2:
                continue  # 没有其他路径可选
            
            # 创建一个邻近解
            neighbor_solution = best_routing.copy()
            
            # 为该流选择一条新的不同路径
            current_path = neighbor_solution[flow_id_to_modify]
            new_path = random.choice([p for p in candidate_paths if p != current_path])
            if not new_path:
                continue
            
            neighbor_solution[flow_id_to_modify] = new_path
            
            # 评估邻近解
            objective_value = self._evaluate_objective(
                neighbor_solution, scheduled_flows, all_flows, current_routing
            )
            
            # 如果找到更优解，则更新
            if objective_value < best_objective:
                best_objective = objective_value
                best_routing = neighbor_solution
                logger.debug(f"迭代 {iteration}: 找到更优解，目标值={objective_value:.4f}")
        
        logger.info(f"迭代优化完成，最优目标值={best_objective:.4f}")
        return best_routing
    
    def _evaluate_objective(self, routing_solution: Dict[str, List[str]],
                          scheduled_flows: List[Dict], 
                          all_flows: List[Dict],
                          base_routing: Dict[str, List[str]]) -> float:
        """
        评估路由方案的目标函数值 - 实现公式(16)
        
        Args:
            routing_solution: 候选路由方案
            scheduled_flows: 调度流
            all_flows: 所有流
            base_routing: 基础路由方案
            
        Returns:
            目标函数值（平均最大流完成时间）
        """
        try:
            # 构建完整的路由方案
            complete_routing = base_routing.copy()
            complete_routing.update(routing_solution)
            
            # 准备预测数据
            flows_to_predict = []
            for flow in scheduled_flows:
                flow_copy = flow.copy()
                flow_id = flow["flow_id"]
                if flow_id in complete_routing:
                    flow_copy["path"] = complete_routing[flow_id]
                flows_to_predict.append(flow_copy)
            
            if not flows_to_predict:
                return 0.0
            
            # 使用预测服务评估流完成时间
            predicted_fcts = self.prediction_service.predict_fct(flows_to_predict, all_flows)
            
            # 按作业分组计算最大流完成时间
            job_max_fcts = defaultdict(float)
            for flow, fct in zip(flows_to_predict, predicted_fcts):
                job_name = flow["job_name"]
                job_max_fcts[job_name] = max(job_max_fcts[job_name], fct)
            
            # 计算平均值
            if job_max_fcts:
                return sum(job_max_fcts.values()) / len(job_max_fcts)
            else:
                return 0.0
                
        except Exception as e:
            logger.warning(f"评估目标函数时出错: {e}")
            return float('inf')  # 返回最差值
    
    def _update_modification_flags(self, optimized_flows: Dict[str, List[str]]):
        """
        更新流的修改标志
        
        Args:
            optimized_flows: 优化后的流路由
        """
        for flow_id in optimized_flows:
            self.modification_flags[flow_id] = True
    
    def set_current_time(self, current_time: float):
        """
        设置当前时间（用于流分类）
        
        Args:
            current_time: 当前仿真时间
        """
        self.current_time = current_time
    
    def reset_modification_flags(self):
        """重置所有修改标志"""
        self.modification_flags.clear()
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """
        获取优化统计信息
        
        Returns:
            统计信息字典
        """
        return {
            "max_iterations": self.max_iterations,
            "k_candidate_paths": self.k_candidate_paths,
            "omega1": self.omega1,
            "omega2": self.omega2,
            "current_time": self.current_time,
            "decision_window": self.decision_window,
            "modified_flows": len(self.modification_flags)
        } 
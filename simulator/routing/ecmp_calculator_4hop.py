"""
强制4跳链路的ECMP计算器
"""

import networkx as nx
import random
from typing import List, Tu<PERSON>, Optional


class ECMPCalculator4Hop:
    """
    强制4跳链路的ECMP路径计算器
    只选择4跳（5个节点）的路径，忽略更短的路径
    """
    
    def __init__(self, topo_file: str):
        """
        Initialize ECMP calculator with network topology file.
        
        Args:
            topo_file (str): Path to GML topology file
        """
        self.topo_file = topo_file
        self.graph = None
        self.host_nodes = []
        self._load_topology()
    
    def _load_topology(self) -> None:
        """
        Load topology from GML file and identify host nodes.
        """
        try:
            self.graph = nx.read_gml(self.topo_file, label='id')
        except Exception as e:
            raise RuntimeError(f"Failed to load topology file {self.topo_file}: {e}")
        
        # Identify host nodes (assuming they are labeled with 'H' prefix)
        self.host_nodes = []
        for node_id in self.graph.nodes():
            node_data = self.graph.nodes[node_id]
            if 'label' in node_data and node_data['label'].startswith('H'):
                self.host_nodes.append(node_id)
        
        self.host_nodes.sort(key=lambda x: int(self.graph.nodes[x]['label'][1:]))  # Sort by host number
        
        if len(self.host_nodes) == 0:
            raise ValueError("No host nodes found in topology")
    
    def get_host_node_id(self, host_index: int) -> int:
        """
        Get the graph node ID for a given host index (0-based).
        
        Args:
            host_index (int): Host index (0 for H1, 1 for H2, etc.)
            
        Returns:
            int: Graph node ID for the host
        """
        if 0 <= host_index < len(self.host_nodes):
            return self.host_nodes[host_index]
        else:
            raise IndexError(f"Host index {host_index} out of range [0, {len(self.host_nodes)-1}]")
    
    def find_all_4hop_paths(self, source_node: int, dest_node: int) -> List[List[int]]:
        """
        Find all 4-hop paths between two nodes (强制4跳路径)。
        只保留中间跳点全为S*或P*的路径。
        """
        try:
            all_4hop_paths = []
            for path in nx.all_simple_paths(self.graph, source_node, dest_node, cutoff=5):
                if len(path) == 5:
                    # 检查中间跳点类型
                    labels = [self.graph.nodes[n]['label'] for n in path]
                    middle = labels[1:-1]
                    if all(x.startswith('S') or x.startswith('P') for x in middle):
                        all_4hop_paths.append(path)
            return all_4hop_paths
        except nx.NetworkXNoPath:
            return []
        except Exception as e:
            print(f"Error finding 4-hop paths from {source_node} to {dest_node}: {e}")
            return []
    
    def select_random_path(self, paths: List[List[int]]) -> List[int]:
        """
        Randomly select one path from a list of paths.
        
        Args:
            paths (List[List[int]]): List of paths
            
        Returns:
            List[int]: Selected path as list of node IDs
        """
        if not paths:
            return []
        return random.choice(paths)
    
    def calculate_ecmp_for_flow_4hop(self, source_host_index: int, dest_host_index: int) -> List[List[str]]:
        """
        Calculate 4-hop ECMP routes for a flow between two hosts.
        
        Args:
            source_host_index (int): Source host index (0-based)
            dest_host_index (int): Destination host index (0-based)
            
        Returns:
            List[List[str]]: A list of all 4-hop routes, where each route is a list of node labels.
        """
        if source_host_index == dest_host_index:
            source_host_id = self.get_host_node_id(source_host_index)
            source_label = self.graph.nodes[source_host_id]['label']
            return [[source_label]]
        
        source_node_id = self.get_host_node_id(source_host_index)
        dest_node_id = self.get_host_node_id(dest_host_index)
        
        all_4hop_paths = self.find_all_4hop_paths(source_node_id, dest_node_id)
        
        if not all_4hop_paths:
            # Fallback logic remains the same
            print(f"Warning: No 4-hop path found between host {source_host_index} and host {dest_host_index}")
            try:
                source_label = self.graph.nodes[source_node_id]['label']
                dest_label = self.graph.nodes[dest_node_id]['label']
                
                source_switches = [n for n in self.graph.adj[source_node_id] if self.graph.nodes[n].get('label', '').startswith('S')]
                dest_switches = [n for n in self.graph.adj[dest_node_id] if self.graph.nodes[n].get('label', '').startswith('S')]
                core_switches = [n for n in self.graph.nodes() if self.graph.nodes[n].get('label', '').startswith('P')]
                
                if source_switches and dest_switches and core_switches:
                    artificial_path = [source_node_id, source_switches[0], core_switches[0], dest_switches[0], dest_node_id]
                    print(f"Created artificial 4-hop path between host {source_host_index} and host {dest_host_index}")
                    all_4hop_paths = [artificial_path]
                else:
                    error_message = f"无法为 {source_label} 和 {dest_label} 创建人工4跳路径。根据要求，在此处停止运行以便调试。"
                    print(error_message)
                    raise RuntimeError(error_message)
            except Exception as e:
                print(f"Error creating artificial 4-hop path: {e}")
                raise RuntimeError(f"No path found between host {source_host_index} and host {dest_host_index}")

        all_path_labels = []
        for path_ids in all_4hop_paths:
            path_labels = [self.graph.nodes[node_id].get('label', str(node_id)) for node_id in path_ids]
            all_path_labels.append(path_labels)
        
        return all_path_labels
    
    def analyze_4hop_availability(self) -> dict:
        """
        分析网络中4跳路径的可用性
        
        Returns:
            dict: 4跳路径分析结果
        """
        total_pairs = 0
        pairs_with_4hop = 0
        max_4hop_paths = 0
        
        for src in range(len(self.host_nodes)):
            for dst in range(len(self.host_nodes)):
                if src != dst:
                    total_pairs += 1
                    src_id = self.get_host_node_id(src)
                    dst_id = self.get_host_node_id(dst)
                    
                    paths_4hop = self.find_all_4hop_paths(src_id, dst_id)
                    if paths_4hop:
                        pairs_with_4hop += 1
                        max_4hop_paths = max(max_4hop_paths, len(paths_4hop))
        
        return {
            'total_host_pairs': total_pairs,
            'pairs_with_4hop': pairs_with_4hop,
            '4hop_coverage': pairs_with_4hop / total_pairs * 100 if total_pairs > 0 else 0,
            'max_4hop_ecmp_paths': max_4hop_paths
        }
    
    def get_topology_info(self) -> dict:
        """
        Get basic information about the loaded topology.
        
        Returns:
            dict: Topology information including 4-hop analysis
        """
        basic_info = {
            'num_nodes': len(self.graph.nodes()),
            'num_edges': len(self.graph.edges()),
            'num_hosts': len(self.host_nodes),
            'host_nodes': [self.graph.nodes[host_id]['label'] for host_id in self.host_nodes]
        }
        
        # Add 4-hop analysis
        hop4_analysis = self.analyze_4hop_availability()
        basic_info.update(hop4_analysis)
        
        return basic_info 
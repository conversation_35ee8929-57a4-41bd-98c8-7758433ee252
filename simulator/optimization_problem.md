
# 系统模型

我们考虑一个多租户GPU集群，该集群包含 $|S|$ 台服务器，记为 $S = \{S_1, S_2, \ldots, S_{|S|}\}$。每台服务器 $S_i$ 配备 $N_i$ 块GPU。集群中所有GPU具有相同的规格和理论峰值性能。服务器内部GPU通过NVLink互连，服务器之间则通过InfiniBand网络互连，该网络拓扑可抽象为有向图 $\mathcal{G}_{net} = (\mathcal{V}, \mathcal{E})$。

一段调度间隔内，系统中有一组等待调度或正在运行的深度学习训练作业 $J = \{J_1, J_2, \ldots, J_{|J|}\}$ 。每个作业 $J_k$ 的训练目标可以用需要处理的总样本数量 $E_k$ 来量化。调度器的核心任务是为每个作业确定资源分配向量 $\mathbf{a}_k = (a_{k,1}, a_{k,2}, \ldots, a_{k,|S|})$，其中 $a_{k,i}$ 是从服务器 $S_i$ 分配给作业 $J_k$ 的GPU数量。

对于每个训练作业 $J_k$，其数据处理策略由两个基础参数决定，这些参数由用户预设并在作业的整个生命周期中保持不变。首先是**有效每GPU批量大小 (Effective Per-GPU Batch Size)**，记为 $m_k$，它从逻辑上定义了单个GPU在一次完整的模型更新周期内所处理的样本总数。其次是**梯度累积步数 (Gradient Accumulation Steps)**，记为 $s_k$，它是一种常用的技术，用于在不增加显存占用的情况下，模拟出更大的逻辑批量。

基于这两个固定的基础参数，我们可以推导出**微批量大小 (Micro-Batch Size)**，记为 $b_k$。它代表了在单次物理的前向/后向传播过程中，单个GPU实际处理的样本数量。为了达到 $m_k$ 的有效批量大小，每个GPU需要执行 $s_k$ 次微批量计算，并在此过程中累积梯度。因此，微批量大小由下式给出：
$$ b_k = \frac{m_k}{s_k} \quad (1)$$

最终，我们定义**有效总批量大小 (Effective Total Batch Size)**，记为 $M_k$。这是整个集群在一个完整的模型更新周期内，为该作业处理的总样本数量，是衡量模型统计效率的关键指标。该值是动态的，因为它直接取决于调度器为作业分配的GPU总数 $n_k = \sum_{i=1}^{|S|} a_{k,i}$。其计算方式为将所有已分配GPU的有效批量进行汇总：
$$ M_k(n_k) = n_k \times m_k \quad (2)$$

深度学习训练的性能评估需考虑计算与通信两个主要方面。在计算方面，我们建模单个GPU对微批量大小为 $b$ 的样本进行一次前向/后向传播（forward/backward pass）的计算时间 $t_{compute}(b)$。这是一个线性模型，用于捕捉计算开销的结构：

$$ t_{compute}(b) = \alpha + \beta \times b \quad (3) $$

其中，$\alpha$ 代表与批量大小无关的固定开销（例如，模型加载或内核启动时间），$\beta$ 则捕捉与样本数量成比例的变量开销（例如，矩阵乘法或激活函数计算）。对于作业 $J_k$，其计算过程涉及 $s_k$ 次这样的微批量计算，以完成梯度累积。

在通信方面，当作业 $J_k$ 分配到 $n_k$ 个GPU时，Ring All-Reduce会在整个系统中产生通信开销。这些通信可以分为**节点内通信**和**节点间通信**。GPU的布局策略会显著影响通信开销。在同一节点上紧密部署GPU可以减少跨节点的网络通信，从而提升整体的同步效率。基于这一观察，我们根据GPU的物理布局采用不同的参数来建模**通信时间** $T_{comm}$。设 $K = n_k$ 为分配给作业 $J_k$ 的GPU总数，$N$ 为被该作业占用的物理节点数量（即至少分配了一个GPU的服务器数目），则通信时间建模为：

$$
T_{comm}(\mathbf{a}_k, m_k) = \begin{cases}
0 & \text{if } K = 1 \\
\alpha_{comm}^{local} + \beta_{comm}^{local} \cdot (K - 2) & \text{if } N = 1, K \geq 2 \\
\alpha_{comm}^{node} + \beta_{comm}^{node} \cdot (K - 2) & \text{otherwise}
\end{cases} \quad (4)
$$

其中，$\alpha_{comm}^{local}$ 和 $\beta_{comm}^{local}$ 是当所有GPU都部署在同一节点上时的常数项和回归参数，$\alpha_{comm}^{node}$ 和 $\beta_{comm}^{node}$ 是当GPU跨多个节点分布时的对应参数。通常情况下，$\alpha_{comm}^{local} < \alpha_{comm}^{node}$ 且 $\beta_{comm}^{local} < \beta_{comm}^{node}$，反映了节点内部署的通信优势。

基于上述计算和通信模型，我们可以构建作业 $J_k$ 的完整单次迭代时间。现代深度学习框架能够通过流水线技术部分重叠梯度计算与网络通信，其重叠程度取决于模型结构等因素。考虑到这种计算-通信重叠效应，作业 $J_k$ 的单次迭代时间建模为：

$$
t_k^{iter} = s_k \times t_{compute}(b_k) + (t_{compute}(b_k)^{\gamma} + T_{comm}(\mathbf{a}_k, m_k)^{\gamma})^{1/\gamma} \quad (5)
$$

其中，第一项表示完成梯度累积所需的总计算时间，第二项通过参数 $\gamma$ 控制的广义均值来建模计算与通信的重叠。

基于迭代时间，我们定义作业的训练吞吐量 $R_k = \frac{n_k \times m_k}{t_k^{iter}}$（样本/秒），它衡量了单位时间内该作业能够处理的样本数量。最终，作业 $J_k$ 的**完成时间** $T_k$ 定义为：

$$T_k = \frac{E_k}{R_k} = \frac{E_k \cdot t_k^{iter}}{n_k \times m_k} \quad (6)$$

其中 $E_k$ 是作业需要处理的总样本数量。


## 2. 问题建模

我们的目标是最小化所有作业的平均作业完成时间 $T_k$。这形成了一个庞大的联合优化问题，其决策变量包括GPU分配向量 $\{\mathbf{a}_k\}$ 和路由决策变量 $\{x_{f,j}\}$。我们将联合优化问题表述为：
$$\min_{\{\mathbf{a}_k\}, \{x_{f,j}\}} \frac{1}{|J|} \sum_{k=1}^{|J|} T_k \quad (5)$$
**s.t.**
$$\sum_{k=1}^{|J|} a_{k,i} \leq N_i, \quad \forall i \in \{1, \ldots, |S|\} \quad (6)$$
$$a_{k,i} \in \mathbb{Z}_{\geq 0}, \quad \forall k, i \quad (7)$$
$$\sum_{i=1}^{|S|} a_{k,i} = 0 \quad \text{or} \quad \sum_{i=1}^{|S|} a_{k,i} \geq 1, \quad \forall k \quad (8)$$
$$\sum_{j=1}^{|\mathcal{P}_f|} x_{f,j} = 1, \quad \forall f \in \mathcal{F}_{inter} \quad (9)$$
$$x_{f,j} \in \{0, 1\}, \quad \forall f, j \quad (10)$$
上述优化问题的决策变量包括资源分配向量 $\{\mathbf{a}_k\}_{k=1}^{|J|}$ 和路由决策变量 $\{x_{f,j}\}$，并涉及多个约束条件。约束(6)是**服务器资源约束**，确保在任意服务器 $S_i$ 上分配给所有作业的GPU总数不能超过该服务器的物理GPU容量 $N_i$。 约束(7)定义了**分配变量的离散性**，即分配给作业的GPU数量 $a_{k,i}$ 必须为非负整数。约束(8)是**作业有效分配约束**，保证每个作业要么不被分配任何资源，要么至少分配一个GPU，以避免无效的零资源分配。约束(9)是**路径选择唯一性约束**，要求每个节点间的数据流 $f$ 必须且只能选择一条网络路径进行传输。约束(10)定义了**路由决策的二进制性质**，即对于流 $f$ 的任意候选路径 $j$，决策变量 $x_{f,j}$ 只能取值为1（选择）或0（不选择）。

该优化问题具有极高的计算复杂性，其NP-hard特性体现在三个核心层面的挑战。
首先是搜索空间的组合爆炸。资源分配向量 $\{\mathbf{a}_k\}_{k=1}^{|J|}$的可能取值数量为 $\prod_{i=1}^{|S|} (N_i+1)^{|J|}$，路由决策变量 $x_{f,j}$ 的可能取值数量为 $\prod_{f \in \mathcal{F}_{inter}} |\mathcal{P}_f|$，使得总搜索空间达到两者的乘积，随着作业数量、服务器数量和路径数量的增加呈指数级增长。

其次，变量耦合导致非线性特性。多条流在同一链路上竞争带宽会引发复杂的拥塞效应，使得流完成时间 $D_f$ 成为所有流路由决策的高度非线性函数，这进而导致整体目标函数呈现非凸性，难以通过传统优化方法找到全局最优解。

最后，网络资源竞争的复杂性。多个节点间流在共享链路上的并发传输会产生非线性带宽竞争效应，每个流的传输时间不仅依赖自身路径选择，还受其他流决策和网络整体负载的动态影响。链路带宽约束将这种全局竞争显式纳入可行解空间，形成复杂的几何结构，难以构建精确的解析模型来捕捉带宽共享的真实机制。

为应对上述挑战，我们提出一种分层解耦框架，将原问题分解为两个在不同时间尺度上协同工作的子问题：上层的资源分配和下层的动态路由。



## 3.分层解耦框架

为解决原优化问题的NP-hard复杂性，我们引入分层解耦框架，将问题分解为两个在不同时间尺度上协同工作的子问题。上层在**分钟级**的较长调度间隔内进行宏观资源分配，以最大化集群有效吞吐量；下层则在实时通信环境中处理动态路由优化，以最小化各作业的通信时间。

这一分层设计的核心在于对整体优化目标的逐级分解。最终目标是最小化平均作业完成时间 $T_k = \frac{E_k}{R_k}$，其中 $R_k$ 代表作业 $J_k$ 的训练吞吐量。根据系统模型，$R_k = \frac{n_k \times m_k}{t_k^{iter}}$，它明确依赖于两个关键因素：GPU分配数量 $n_k$ 和单次迭代时间 $t_k^{iter}$。前者由上层资源分配决策 $\mathbf{a}_k$ 直接决定，后者则由公式(5)给出，包含梯度计算时间$t_{compute}$和通信时间 $T_{comm}$。在下层优化中，通信时间进一步受节点间流的路由决策影响，表现为具体的通信延迟 $t_{comm}^k$。因此，上层通过优化 $\mathbf{a}_k$ 来直接控制 $n_k$，下层通过优化路由决策 $\{x_{f,j}\}$ 来最小化 $t_{comm}^k$，两者协同提升 $R_k$ 以缩短作业完成时间。

然而，资源分配变量 $\{\mathbf{a}_k\}$ 和路由变量 $\{x_{f,j}\}$ 存在强耦合关系：上层的分配决策 $\mathbf{a}_k$ 决定了Ring All-Reduce产生的节点间流集合 $\mathcal{F}_{inter}$，这些流的数量、大小和源-目的对都直接由GPU在各服务器上的分布确定；下层的路由决策 $\{x_{f,j}\}$ 则决定了每个流 $f \in \mathcal{F}_{inter}$ 在网络中的传输路径，进而影响网络拥塞和 $t_{comm}^k$，最终反馈到 $T_k$。这种循环依赖使得联合优化在计算上不可行。同时，网络负载的动态变化（如突发拥塞、链路故障）要求路由决策具备实时响应能力，而资源分配的频繁调整会导致训练中断和系统不稳定。

通过分层解耦，我们将这一复杂问题在时间尺度上分离：上层在较长时间间隔内优化资源分配，使用参数化的通信模型 $T_{comm}(\mathbf{a}_k, m_k)$（公式4）来近似评估不同分配方案的通信开销，从而避免求解具体的路由细节；下层在实时环境中响应网络状态变化，利用预测模型快速优化路由决策。这种设计将原问题的指数级搜索空间分解为两个可独立处理的子空间，同时通过上层决策定义下层输入、下层性能反馈指导上层调整的迭代机制，实现协同优化并保证整体解的质量。



### 3.1 上层-面向Goodput的协同资源分配

在上层框架中，调度器在每个较长的调度间隔开始时运行，其核心目标是在满足物理资源限制的前提下，综合考虑当前正在运行的任务以及新到达的作业 $J$，制定一个统一的GPU资源分配方案。该方案将在整个调度间隔内生效和执行，以联合优化集群的整体有效吞吐量与资源利用率。

深度学习训练的性能优化面临一个根本性的权衡挑战：**系统吞吐量**与**统计效率**之间的平衡。系统吞吐量指单位时间内处理的训练样本数量，通常可以通过增加GPU数量来提升并行度；统计效率则指处理每个训练样本所能带来的模型训练进展，受总批量大小、学习率等因素影响。关键的矛盾在于，虽然增加GPU数量能够提高系统吞吐量和计算利用率，但由此导致的总批量大小增大通常会降低统计效率（即使学习率已优化），意味着需要处理更多数据才能达到同等的训练效果。

因此我们引入**Goodput**作为统一的性能优化目标，以在系统吞吐量$R_k$与统计效率$\eta_k$之间找到动态平衡点。Goodput定义为二者的乘积，即$G_k = R_k \times \eta_k$，它可以被理解为"对训练进度有用的那一部分吞吐量"。我们将统计效率$\eta_k$建模为依赖于有效总批量大小$M_k$的单调递增函数，即$\eta_k(M_k)$，它量化了更大批量大小如何提升模型收敛速度，但受制于边际收益递减（diminishing returns）。这种建模基于经验观察和文献支持，通常通过拟合历史训练数据或分析模型行为来参数化。

我们选择固定部分参数（如用户预设的有效每GPU批量大小$m_k$、梯度累积步数$s_k$、微批量大小$b_k = m_k / s_k$）的理由是为了尊重用户的配置偏好、维持训练过程的连续性和稳定性。这些参数由用户在作业提交时预设，并在整个生命周期中保持不变，避免了动态调整可能带来的训练中断或额外开销。在我们的框架中，虽然$m_k$和$s_k$是固定的，但通过优化GPU分配数量$n_k$，我们仍能动态调整有效总批量大小$M_k = n_k \times m_k$，从而影响统计效率$\eta_k$并实现性能权衡。

单纯最大化Goodput可能导致过度分配GPU（例如，为少数作业分配过多资源），从而降低整体利用率并增加资源消耗。因此，我们构建了一个多目标优化问题：第一个目标是最大化集群的总Goodput $\sum G_k$；第二个目标是最小化总GPU消耗$\sum n_k$，以在保证高Goodput的前提下释放冗余资源，支持更多作业并发执行。

该优化问题可表述为：
$$
\max_{\{\mathbf{a}_k\}_{k=1}^{|J|}} \sum_{k=1}^{|J|} G_k, 
\quad \min_{\{\mathbf{a}_k\}_{k=1}^{|J|}} \sum_{k=1}^{|J|} n_k \quad (12)
$$
**s.t.**
$$
\begin{align}
& (6), (7), (8) \\
& M_k(n_k) \ge M_k^{\min}, \quad \forall k \in J \text{ s.t. } n_k > 0 \tag{13}
\end{align}
$$

其中，作业 $J_k$ 的Goodput $G_k$ 定义为：
$$G_k(\mathbf{a}_k) = R_k(n_k, m_k, s_k) \times \eta_k(M_k)\quad (11)$$

训练吞吐量$R_k$表示单位时间内处理的样本数量，根据公式(5)和(6)，由资源分配向量$\mathbf{a}_k$间接确定：$\mathbf{a}_k$决定GPU总数$n_k = \sum a_{k,i}$和通信时间$T_{comm}(\mathbf{a}_k, m_k)$，进而确定单次迭代时间$t_k^{iter}$，最终得到$R_k = \frac{n_k \times m_k}{t_k^{iter}}$。在分层框架中，上层调度器通过使用参数化的通信时间估计模型，可以基于GPU分配数量$n_k$和固定的作业参数快速计算吞吐量$R_k$，从而避免直接建模下层复杂路由信息，实现子问题解耦。

该优化问题的决策变量为资源分配向量集合 $\{\mathbf{a}_k\}_{k=1}^{|J|}$。约束 (6), (7), (8)直接继承自全局问题定义，分别对应**服务器资源约束**、**分配变量的非负整数特性**以及**作业分配的有效性**。约束(13)是**最小有效总批量大小约束**，它要求任何被调度执行的作业，其总批量必须达到预设的最小值 $M_k^{\min}$，以保证训练的稳定性和效率。

为求解此多目标优化问题，我们采用**非支配排序遗传算法II (NSGA-II)**。算法的输出是一个帕累托最优前沿，代表了在Goodput和资源消耗之间的多种最优权衡。最终，系统会根据预设的运营策略（例如，优先最大化Goodput或选择提供最佳平衡的"拐点"解）从该前沿中选择一个具体的操作点。



### 3.2 下层-动态预测性路由

上层调度器从帕累托前沿选择一个具体的操作点后，便确定了最优的全局资源配置 $\{\mathbf{a}_k\}$，该配置定义了所有作业产生的节点间通信流集合 $\mathcal{F}_{inter}$。下层路由器的核心任务是为这些流实时规划网络路径，以最小化通信延迟。

对于每个节点间流 $f \in \mathcal{F}_{inter}$，存在一个候选路径集合 $\mathcal{P}_f$。我们引入二进制路由决策变量 $x_{f,j} \in \{0, 1\}$，表示流 $f$ 是否选择其第 $j$ 条路径。流 $f$ 的传输时间 $D_f$ 取决于其选择的路径以及该路径上由其他流造成的拥塞。由于Ring All-Reduce的同步特性，作业 $J_k$ 的通信时间由其最慢的节点间流决定，即通信瓶颈：
$$
t_{comm}^k = \max_{f \in \mathcal{F}_k \cap \mathcal{F}_{inter}} D_f \quad (3)
$$

然而，在动态、拥塞的实时环境中，无法直接评估任意路由决策对 $D_f$ 的影响。为了解决这一难题，我们设计并引入一个预训练的**未来感知时空图注意力网络(STGNN)**，记为 $\mathcal{M}$。该模型充当一个高精度的延迟评估器，能够根据给定的全局路由决策 $\{x_{f',j'}\}$，精准预测出任意目标流 $f$ 的完成时间 $\hat{D}_f$。通过引入该预测模型，下层优化问题被清晰地定义为最小化所有作业通信开销的平均值：
$$
\min_{\{x_{f,j}\}} \frac{1}{|J|} \sum_{k=1}^{|J|} t_{comm}^k = \min_{\{x_{f,j}\}} \frac{1}{|J|} \sum_{k=1}^{|J|} \max_{f \in \mathcal{F}_k \cap \mathcal{F}_{inter}} \hat{D}_f \quad (14) 
$$

s.t.
$$
\begin{align}
& (9), (10) \\
& \hat{D}_f = \mathcal{M} (f, \{x_{f',j'}\}_{f' \in \mathcal{F}_{inter}}, \mathcal{G}_{net}), \quad \forall f \in \mathcal{F}_{inter} \quad (15)
\end{align}
$$

约束(9)和(10)继承自全局问题。约束(15)将优化目标与决策变量关联起来，明确指出流的预测完成时间 $\hat{D}_f$ 是由预测模型 $\mathcal{M}$ 根据所有流的路由决策 $\{x_{f',j'}\}$ 计算得出的。

由于该路由选择问题是NP-hard的，为在实时环境中高效求解，我们设计了一种基于预测的两阶段启发式算法。在任意时刻 $t$，我们将网络中的流划分为调度流 $S(t)$、背景流 $B(t)$ 和未来流 $F(t, \Delta t)$。该算法的核心思想是，首先为那些最可能引发拥塞的**冲突流**（conflict flows）筛选出一个高质量的候选路径子集，然后在此缩小的搜索空间内求解一个简化的优化问题。

我们定义在时刻 $t$ 拥有调度流的任务集合为 $A(t)$，$S_k(t) = S(t) \cap \mathcal{F}_k$ 为作业 $J_k$ 的调度流集合。优化目标是在一个缩小的路径空间内，最小化各作业最大流完成时间的平均值：
$$
\min_{\substack{p(f) \in P'(f) \\ \forall f \in C}} \frac{1}{|A(t)|} \sum_{k \in A(t)} \max_{s \in S_k(t)} \hat{D}_s(p) \quad (16)
$$

第一阶段是**识别冲突流并生成候选路径**。一个未来流 $f \in F(t, \Delta t)$ 被定义为冲突流，如果其默认路径（如ECMP路径）与任何当前调度流的默认路径存在重叠。其集合 $C$ 定义如下：
$$
C = \{f \in F(t, \Delta t) | p^{(0)}(f) \cap \left( \bigcup_{s \in S(t)} p^{(0)}(s) \right) \neq \emptyset \text{ and } \mu(f) = 0\} \quad (17)
$$
其中修改标志 $\mu(f)$ 确保每个流在一个决策窗口内只被重路由一次。随后，对于每个冲突流 $f \in C$，我们通过代价函数 $\psi(p, t)$ 对其所有可用路径进行评估，并筛选出 $K$ 条代价最低的路径构成候选路径集 $P'(f)$。代价函数 $\psi$ 对路径的潜在拥塞风险进行量化惩罚：
$$
\psi(p, t) = \sum_{e \in p} [\omega_1 \cdot I(e \in \bigcup_{s \in S(t)} p^{(0)}(s)) + \omega_2 \cdot \nu_e(t)] \quad (19)
$$
该函数通过加权项惩罚与当前调度流路径重叠的链路（权重$\omega_1$），以及预计被其他未来流共享的链路（权重$\omega_2$），且$\omega_1 > \omega_2 > 0$。

第二阶段是**求解与路径更新**。在构建所有冲突流的候选路径集后，我们通过求解优化问题(16)，在缩小的联合搜索空间 $\times_{f \in C} P'(f)$ 内为每个冲突流找到一条最优路径 $p^*(f)$，并更新其路由状态。


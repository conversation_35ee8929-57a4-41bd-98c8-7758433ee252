import argparse
import collections
import copy
import glob
import json
import math
import multiprocessing
import os
import logging
import time
from typing import Dict, List, Any

import numpy as np
import pandas

from applications import APPLICATIONS
from goodput import GoodputFunction, fit_perf_params
from speedup import SpeedupFunction
from utils import JobInfo, NodeInfo
from pollux import PolluxPolicy
from optimus import OptimusPolicy
from tiresias import TiresiasPolicy

# 导入高保真通信模拟组件
from routing.topology_manager import TopologyManager
from routing.routing_optimizer import RoutingOptimizer
from routing.prediction_service import PredictionService
from collective_communication.flow_generator import FlowGenerator
from collective_communication.communication_simulator import CommunicationSimulator

# 配置日志
logging.basicConfig(level=logging.INFO)


class Job(object):

    pretrain = {}

    def __init__(self, name, application, submission_time,
                 target_num_replicas=None, target_batch_size=None):
        self.name = name
        self.application = application
        self.submission_time = submission_time
        self.target_num_replicas = target_num_replicas
        self.target_batch_size = target_batch_size
        self.completion_time = None
        self.current_time = 0
        self.rescale_time = 0
        self.placement = ()
        self.atomic_bsz = 0
        self.accum_steps = 0
        self.profile = {}
        self.perf_params = None
        self.grad_params = None
        self.best_metric = None
        self.progress = 0.0
        self.epoch = 0
        self.attained_service = 0
        self.num_restarts = None

    @property
    def max_profiled_replicas(self):
        return max((k[1] for k in self.profile), default=0)

    def get_goodput_fn(self):
        app = self.application
        return GoodputFunction(self.perf_params, self.grad_params, app.init_batch_size)

    def get_speedup_fn(self):
        if self.perf_params is None:
            return lambda n, r: r
        app = self.application
        return SpeedupFunction(self.get_goodput_fn(), app.max_batch_size,
                               (app.min_local_bsz, app.max_local_bsz),
                               accumulation=True)

    def update_local_bsz(self, placement):
        app = self.application
        placement = tuple(filter(None, placement))
        num_nodes, num_replicas = len(placement), sum(placement)
        batch_size = self.target_batch_size
        if batch_size is None and self.perf_params is None:
            batch_size = max(app.init_batch_size, app.min_local_bsz * num_replicas)
        if batch_size is None:
            goodput_fn = self.get_goodput_fn()
            _, self.atomic_bsz, self.accum_steps = goodput_fn.optimize(
                num_nodes, num_replicas, app.max_batch_size,
                (app.min_local_bsz, app.max_local_bsz), accumulation=True)
        else:
            local_bsz = math.ceil(batch_size / num_replicas - 1e-8)
            # 固定累积梯度步数为1，简化计算
            self.accum_steps = 1
            self.atomic_bsz = math.ceil(local_bsz / (self.accum_steps + 1) - 1e-8)
        count = num_replicas * (self.accum_steps + 1)
        self.atomic_bsz = min(self.atomic_bsz, int(app.max_batch_size / count))
        # 确保atomic_bsz不超过应用程序的max_local_bsz限制，避免插值范围错误
        self.atomic_bsz = min(self.atomic_bsz, app.max_local_bsz)

    def update_params(self, num_nodes, num_replicas, local_bsz,
                      step_time, sync_time, grad_sqr, grad_var):
        self.grad_params = (grad_sqr, grad_var)
        if (num_nodes, num_replicas, local_bsz) in self.profile:
            return
        self.profile[num_nodes, num_replicas, local_bsz] = step_time, sync_time
        num_nodes = np.array([key[0] for key in self.profile])
        num_replicas = np.array([key[1] for key in self.profile])
        local_bsz = np.array([key[2] for key in self.profile])
        step_time = np.array([val[0] for val in self.profile.values()])
        sync_time = np.array([val[1] for val in self.profile.values()])
        compute_time = step_time - sync_time
        self.perf_params = fit_perf_params(
            num_nodes, num_replicas, local_bsz, compute_time, step_time)

    def step(self, seconds, interference=0.0, communication_time=None):
        if not self.placement:
            # No resources are allocated to this job.
            self.current_time += seconds
            return
        delay = min(self.rescale_time, seconds)
        self.current_time += delay
        self.attained_service += delay * sum(self.placement)
        self.rescale_time -= delay
        seconds -= delay
        while seconds > 0 and self.completion_time is None:
            assert self.epoch < self.application.max_epochs
            # Calculate current job configurations.
            placement = tuple(filter(None, self.placement))
            num_nodes, num_replicas = len(placement), sum(placement)
            local_bsz = self.atomic_bsz
            batch_size = num_replicas * self.atomic_bsz * (self.accum_steps + 1)
            scale = batch_size / self.application.init_batch_size
            # Calculate true (simulated) throughput.
            if communication_time is not None:
                # 使用高保真通信模拟结果
                original_step_time, original_sync_time = \
                    self.application.get_throughput(placement, self.atomic_bsz)
                accum_time = original_step_time - original_sync_time  # 纯计算时间
                sync_time = communication_time  # 使用高保真模拟的通信时间
                step_time = accum_time + sync_time  # 总时间 = 计算时间 + 通信时间
            else:
                # 使用原始的线性估算
                step_time, sync_time = \
                    self.application.get_throughput(placement, self.atomic_bsz)
                accum_time = step_time - sync_time
            # Calculate true (simulated) efficiency.
            grad_sqr, grad_var = \
                self.application.get_grad_stats(batch_size, self.epoch)
            gain = (grad_var + grad_sqr) / (grad_var / scale + grad_sqr)
            # Update the estimated throughput/efficiency parameters.
            self.update_params(num_nodes, num_replicas, self.atomic_bsz,
                               step_time, sync_time, grad_sqr, grad_var)
            # Calculate true (simulated) goodput.
            total_time = step_time + accum_time * self.accum_steps
            goodput = gain / total_time * (1.0 - interference)
            # Update current epoch and progress.
            next_progress = self.application.get_progress(self.epoch + 1)
            if self.progress + goodput * seconds < next_progress:
                # Used up the entire time interval without finishing an epoch.
                self.progress += goodput * seconds
                self.current_time += seconds
                self.attained_service += seconds * sum(self.placement)
                seconds = 0
            else:
                # Crossed an epoch boundary before finishing the time interval.
                self.epoch += 1
                delta = round(float((next_progress - self.progress) / goodput))
                assert delta <= seconds
                completion_epoch = \
                    self.application.get_completion_epoch(batch_size)
                if self.epoch > completion_epoch:
                    self.completion_time = self.current_time + delta
                self.progress = next_progress
                self.best_metric = \
                    self.application.get_best_metric(batch_size, self.epoch)
                self.current_time += delta
                self.attained_service += delta * sum(self.placement)
                seconds -= delta
                # Re-scale batch size between epochs.
            self.update_local_bsz(self.placement)
        self.current_time += seconds  # Add any remaining time.

    def reallocate(self, placement):
        if placement:
            self.placement = tuple(placement)
            self.update_local_bsz(self.placement)
            self.rescale_time = 30  # Start re-scale countdown.
            if self.num_restarts is None:
                self.num_restarts = 0
            else:
                self.num_restarts += 1
        else:  # De-allocate all resources.
            self.placement = ()
            self.atomic_bsz = 0


class Cluster(object):
    def __init__(self, workload, policy, min_nodes, num_gpus=4,
                 max_nodes=None, interference=0.0,
                 low_util=None, high_util=None, enable_routing_optimizer=False):
        assert 1 <= num_gpus <= 4
        self.workload = workload
        self.policy = policy
        self.min_nodes = self.num_nodes = min_nodes
        self.num_gpus = num_gpus
        self.max_nodes = min_nodes if max_nodes is None else max_nodes
        self.interference = interference
        self.low_util = low_util
        self.high_util = high_util
        self.current_time = 0
        if isinstance(policy, PolluxPolicy):
            self.jobs = [Job(row.name, APPLICATIONS[row.application], row.time)
                         for row in workload.itertuples()]
            for job in self.jobs:
                # 强制使用最大batch size，避免复杂的动态优化
                job.target_batch_size = job.application.max_batch_size
        elif isinstance(policy, TiresiasPolicy):
            self.jobs = [Job(row.name, APPLICATIONS[row.application], row.time,
                             target_num_replicas=row.num_replicas,
                             target_batch_size=row.batch_size)
                         for row in workload.itertuples()]
        elif isinstance(policy, OptimusPolicy):
            self.jobs = [Job(row.name, APPLICATIONS[row.application], row.time,
                             target_batch_size=row.batch_size)
                         for row in workload.itertuples()]
        self.allocations = {}
        self.logs = []
        self.utility = []
        
        # 初始化高保真通信模拟组件
        self.logger = logging.getLogger(__name__)
        self.logger.info("初始化高保真通信模拟系统")
        self.enable_routing_optimizer = enable_routing_optimizer
        
        # 性能统计
        self.performance_stats = {
            'communication_simulation': {
                'total_intervals': 0,
                'total_time': 0.0,
                'total_flows_simulated': 0,
                'total_events_processed': 0,
                'batch_predictions': 0,
                'template_cache_hits': 0,
                'avg_flows_per_interval': 0.0,
                'avg_time_per_interval': 0.0
            },
            'prediction_performance': {
                'total_prediction_calls': 0,
                'total_prediction_time': 0.0,
                'avg_prediction_time': 0.0,
                'batch_sizes': [],
                'single_predictions': 0
            }
        }
        
        try:
            # 初始化核心组件
            self.topology_manager = TopologyManager()
            self.prediction_service = PredictionService()
            self.flow_generator = FlowGenerator()
            self.routing_optimizer = RoutingOptimizer(self.prediction_service)
            self.communication_simulator = CommunicationSimulator(
                self.flow_generator, self.topology_manager, self.prediction_service,
                enable_routing_optimizer=self.enable_routing_optimizer
            )
            
            # 设置动态路由优化器
            self.communication_simulator.set_routing_optimizer(self.routing_optimizer)
            
            # 存储每个调度间隔的通信时间结果
            self.communication_times = {}  # {job_name: communication_time}
            self.enable_high_fidelity_comm = True

            # 状态持久化相关字段
            self.previous_placement = {}  # 上一个间隔的placement
            self.saved_communication_state = None  # 保存的通信模拟器状态
            self.cross_interval_jobs = {}  # 跨间隔的作业状态

            # self.logger.info("高保真通信模拟系统初始化完成")
            
        except Exception as e:
            self.logger.warning(f"高保真通信模拟系统初始化失败，回退到原始模式: {e}")
            self.enable_high_fidelity_comm = False

    def step(self, seconds=60):
        # 在间隔开始时处理跨间隔状态
        if self.enable_high_fidelity_comm:
            # self.logger.info("处理跨间隔通信状态")
            self._handle_cross_interval_communication()

        # 执行高保真通信模拟（如果启用）
        if self.enable_high_fidelity_comm:
            self._run_high_fidelity_communication_simulation(seconds)

        # 在间隔结束时保存状态
        if self.enable_high_fidelity_comm:
            self._save_interval_state()
        
        interfere_nodes = set(idx for idx in range(self.num_nodes)
                              if sum(len(set(val)) > 1 and idx in val
                                     for key, val in self.allocations.items()) > 1)
        for job in self.jobs:
            alloc_set = set(self.allocations.get(job.name, []))
            interference = 0.0
            if len(alloc_set) > 1 and any(idx in interfere_nodes for idx in alloc_set):
                interference = self.interference
            
            # 获取高保真通信时间（如果可用）
            communication_time = None
            if self.enable_high_fidelity_comm and job.name in self.communication_times:
                if self.communication_times[job.name] != 0:
                    communication_time = self.communication_times[job.name]
            
            job.step(seconds, interference=interference, communication_time=communication_time)
        self.current_time += seconds
        assert all(job.current_time == self.current_time for job in self.jobs)
        job_infos = self.get_job_infos()
        if job_infos:
            if self.max_nodes > self.min_nodes:
                # Autoscale cluster if needed.
                self.utility.append(self.get_utility(self.num_nodes, job_infos, self.allocations))
                if len(self.utility) > 15:
                    self.utility.pop(0)
                    utility = sum(self.utility) / len(self.utility)
                    if (self.num_nodes > self.min_nodes and self.low_util is not None and utility < self.low_util) or \
                            (self.num_nodes < self.max_nodes and self.high_util is not None and utility > self.high_util):
                        self.autoscale(job_infos)
                        self.utility.clear()
                    print("Utility:", utility)
                print("Nodes:", self.num_nodes)
            # Optimize allocations.
            node_infos = self.get_node_infos()
            self.allocations = {k: v for k, v in self.allocations.items() if k in job_infos}
            results = self.policy.optimize(job_infos, node_infos,
                                           self.allocations, node_infos[0])
            
            # print(f"DEBUG: Policy decision at time {self.current_time}: {results[0]}") # 加上这行
            allocations, desired_nodes = results
            used_gpus = collections.Counter(sum(allocations.values(), []))
            assert all(val <= node_infos[key].resources["nvidia.com/gpu"]
                       for key, val in used_gpus.items())
            for job in self.jobs:
                if allocations.get(job.name) != self.allocations.get(job.name):
                    alloc = allocations.get(job.name, [])
                    placement = []
                    for i in range(len(alloc)):
                        if i == 0 or alloc[i] != alloc[i - 1]:
                            placement.append(1)
                        else:
                            placement[-1] += 1
                    job.reallocate(placement)
            self.allocations = allocations
        self.logs.append({
            "timestamp": self.current_time,
            "num_nodes": self.num_nodes,
            "submitted_jobs": [
                {
                    "name": job.name,
                    "epoch": job.epoch,
                    "progress": job.progress,
                    "num_restarts": job.num_restarts,
                    "allocation": self.allocations.get(job.name, []),
                    "placement": job.placement,
                    "batch_size": job.atomic_bsz * (job.accum_steps + 1) * sum(job.placement),
                    "accum_steps": job.accum_steps,
                    "submission_time": job.submission_time,
                    "completion_time": job.completion_time,
                    "grad_params": job.grad_params,
                }
                for job in self.jobs if job.submission_time <= self.current_time
            ],
        })

    def _run_high_fidelity_communication_simulation(self, interval_duration):
        """
        运行高保真通信模拟，支持跨间隔状态持续

        Args:
            interval_duration: 调度间隔时长（秒）
        """
        # 性能统计：开始计时
        interval_start_time = time.time()
        self.performance_stats['communication_simulation']['total_intervals'] += 1
        
        self.logger.info(f"高保真通信模拟开始，路由优化{'启用' if self.enable_routing_optimizer else '禁用'}")

        try:
            # 获取当前活跃的作业
            active_jobs = [job for job in self.jobs
                          if (self.current_time >= job.submission_time and
                              job.completion_time is None and
                              job.placement and sum(job.placement) > 0)]

            if not active_jobs:
                self.communication_times = {}
                self.previous_placement = {}
                return

            # 构建当前placement字典
            current_placement = {}
            multi_node_jobs = []  # 记录需要跨节点通信的作业
            single_node_jobs = []  # 记录单节点作业

            for job in active_jobs:
                # 将job.placement转换为节点分配列表
                node_allocation = []
                current_node = 0
                for gpu_count in job.placement:
                    for _ in range(gpu_count):
                        node_allocation.append(current_node)
                    current_node += 1
                current_placement[job.name] = node_allocation

                # 判断是否为跨节点作业
                unique_nodes = len(set(node_allocation))
                if unique_nodes > 1:
                    multi_node_jobs.append(job)
                    self.logger.debug(f"多节点作业 {job.name}: placement={job.placement}, 节点分配={node_allocation}, 跨{unique_nodes}个节点")
                else:
                    single_node_jobs.append(job)
                    self.logger.debug(f"单节点作业 {job.name}: placement={job.placement}, 节点分配={node_allocation}, 使用线性插值通信模型")
            
            self.logger.debug(f"运行高保真通信模拟: {len(active_jobs)} 个活跃作业 (多节点: {len(multi_node_jobs)}, 单节点: {len(single_node_jobs)})")

            # 检测placement变化
            placement_changes = self._detect_placement_changes(current_placement)
            self.logger.info(f"Placement变化检测结果: {placement_changes}")

            # 决定是否可以恢复之前的状态
            can_restore_state = (self.saved_communication_state is not None and
                               all(change in ['no_change'] for change in placement_changes.values()))

            if can_restore_state:
                self.logger.info("Placement未变化，尝试恢复之前的通信模拟状态")
                try:
                    self.communication_simulator.load_simulation_state(self.saved_communication_state)
                    # 继续之前未完成的模拟
                    self.communication_times = self._continue_cross_interval_simulation(
                        multi_node_jobs, interval_duration
                    )
                except Exception as e:
                    self.logger.warning(f"状态恢复失败，重新开始模拟: {e}")
                    can_restore_state = False

            if not can_restore_state:
                # 重新开始模拟
                self.logger.info("开始新的通信模拟")
                self.communication_times = self._simulate_multiple_allreduces(
                    active_jobs, multi_node_jobs, single_node_jobs, current_placement, interval_duration
                )

            # 更新previous_placement
            self.previous_placement = current_placement.copy()

            self.logger.debug(f"通信模拟完成: {len(self.communication_times)} 个作业获得通信时间")
            
        except Exception as e:
            self.logger.error(f"高保真通信模拟失败，使用默认通信时间: {e}")
            # 回退到默认通信时间
            self.communication_times = {job.name: 0.0 for job in active_jobs}
        
        finally:
            # 性能统计：结束计时并收集统计信息
            interval_end_time = time.time()
            interval_duration_actual = interval_end_time - interval_start_time
            
            self.performance_stats['communication_simulation']['total_time'] += interval_duration_actual
            
            # 收集通信模拟器的统计信息
            if hasattr(self, 'communication_simulator'):
                sim_stats = self.communication_simulator.get_simulation_stats()
                self.performance_stats['communication_simulation']['total_flows_simulated'] += sim_stats.get('total_flows_simulated', 0)
                self.performance_stats['communication_simulation']['total_events_processed'] += sim_stats.get('total_events_processed', 0)
                
                # 收集预测缓冲统计 - 暂时注释
                # buffer_stats = self.communication_simulator.prediction_buffer.get_buffer_stats()
                # if buffer_stats.get('total_flows', 0) > 0:
                #     self.performance_stats['communication_simulation']['batch_predictions'] += 1
            
            # 收集流模板缓存统计 - 暂时注释
            # if hasattr(self, 'flow_generator'):
            #     template_stats = self.flow_generator.template_manager.get_template_stats()
            #     self.performance_stats['communication_simulation']['template_cache_hits'] += template_stats.get('cached_jobs', 0)
            
            # 更新平均值
            total_intervals = self.performance_stats['communication_simulation']['total_intervals']
            total_time = self.performance_stats['communication_simulation']['total_time']
            total_flows = self.performance_stats['communication_simulation']['total_flows_simulated']
            
            self.performance_stats['communication_simulation']['avg_time_per_interval'] = total_time / total_intervals if total_intervals > 0 else 0.0
            self.performance_stats['communication_simulation']['avg_flows_per_interval'] = total_flows / total_intervals if total_intervals > 0 else 0.0
            
            self.logger.debug(f"间隔通信模拟统计: 耗时{interval_duration_actual:.4f}秒, "
                            f"累计间隔{total_intervals}, 平均耗时{self.performance_stats['communication_simulation']['avg_time_per_interval']:.4f}秒")

    def get_performance_summary(self) -> Dict[str, Any]:
        """
        获取性能统计摘要
        
        Returns:
            性能统计摘要字典
        """
        comm_stats = self.performance_stats['communication_simulation']
        pred_stats = self.performance_stats['prediction_performance']
        
        return {
            '通信模拟性能': {
                '总间隔数': comm_stats['total_intervals'],
                '总耗时(秒)': f"{comm_stats['total_time']:.4f}",
                '平均每间隔耗时(秒)': f"{comm_stats['avg_time_per_interval']:.4f}",
                '总流数': comm_stats['total_flows_simulated'],
                '平均每间隔流数': f"{comm_stats['avg_flows_per_interval']:.2f}",
                '总事件数': comm_stats['total_events_processed'],
                '批量预测次数': comm_stats['batch_predictions'],
                '模板缓存命中': comm_stats['template_cache_hits']
            },
            '预测性能': {
                '总预测调用': pred_stats['total_prediction_calls'],
                '总预测耗时(秒)': f"{pred_stats['total_prediction_time']:.4f}",
                '平均预测耗时(秒)': f"{pred_stats['avg_prediction_time']:.6f}",
                '单流预测次数': pred_stats['single_predictions'],
                '批量大小分布': pred_stats['batch_sizes'][-10:] if pred_stats['batch_sizes'] else []  # 显示最近10个批量大小
            }
        }
    
    def print_performance_summary(self) -> None:
        """打印性能统计摘要"""
        summary = self.get_performance_summary()
        
        print("=" * 60)
        print("性能优化统计摘要")
        print("=" * 60)
        
        print("\n📊 通信模拟性能:")
        for key, value in summary['通信模拟性能'].items():
            print(f"  {key}: {value}")
        
        print("\n🔮 预测性能:")
        for key, value in summary['预测性能'].items():
            print(f"  {key}: {value}")
        
        print("=" * 60)

    def _detect_placement_changes(self, current_placement: dict) -> dict:
        """
        检测placement变化，返回变化类型

        Args:
            current_placement: 当前placement字典 {job_name: [node_indices]}

        Returns:
            变化信息字典 {job_name: change_type}
            change_type可能的值: 'no_change', 'modified', 'new', 'removed'
        """
        changes = {}

        # 检查当前作业的变化
        for job_name, current_alloc in current_placement.items():
            if job_name not in self.previous_placement:
                changes[job_name] = 'new'
            elif not self._is_placement_identical(
                self.previous_placement[job_name], current_alloc
            ):
                changes[job_name] = 'modified'
            else:
                changes[job_name] = 'no_change'

        # 检查被移除的作业
        for job_name in self.previous_placement:
            if job_name not in current_placement:
                changes[job_name] = 'removed'

        return changes

    def _is_placement_identical(self, old_placement: list, new_placement: list) -> bool:
        """
        判断两个placement是否完全相同

        Args:
            old_placement: 旧的节点分配列表
            new_placement: 新的节点分配列表

        Returns:
            是否相同
        """
        if old_placement is None or new_placement is None:
            return old_placement == new_placement

        return (len(old_placement) == len(new_placement) and
                all(a == b for a, b in zip(sorted(old_placement), sorted(new_placement))))

    def _continue_cross_interval_simulation(self, multi_node_jobs: list, interval_duration: float) -> dict:
        """
        继续跨间隔的通信模拟

        Args:
            multi_node_jobs: 多节点作业列表
            interval_duration: 间隔时长

        Returns:
            通信时间字典
        """
        try:
            # 继续执行之前未完成的事件循环
            communication_times = self.communication_simulator.run(
                {}, multi_node_jobs, interval_duration
            )

            # 保存当前状态以备下次使用
            self.saved_communication_state = self.communication_simulator.save_simulation_state()

            return communication_times
        except Exception as e:
            self.logger.error(f"跨间隔模拟继续失败: {e}")
            return {job.name: 0.0 for job in multi_node_jobs}

    def _simulate_multiple_allreduces(self, active_jobs: list, multi_node_jobs: list,
                                    single_node_jobs: list, current_placement: dict,
                                    interval_duration: float) -> dict:
        """
        在间隔内模拟多次Ring All-Reduce，返回平均通信时间

        Args:
            active_jobs: 所有活跃作业
            multi_node_jobs: 多节点作业列表
            single_node_jobs: 单节点作业列表
            current_placement: 当前placement字典
            interval_duration: 间隔时长

        Returns:
            平均通信时间字典
        """
        communication_times = {}

        # 为单节点作业设置默认通信时间
        for job in single_node_jobs:
            communication_times[job.name] = 0.0
            self.logger.debug(f"单节点作业 {job.name} 通信时间=0.0（无跨节点通信）")

        # 对多节点作业进行真正的多次Ring All-Reduce模拟
        if multi_node_jobs:
            self.logger.info(f"开始多次Ring All-Reduce模拟: {len(multi_node_jobs)} 个多节点作业")

            # 阶段1: 流生成（仅多节点作业）
            all_potential_flows = self.flow_generator.generate_all_potential_flows(
                multi_node_jobs, current_placement, self.topology_manager
            )

            if all_potential_flows:
                # 设置all_known_flows到通信模拟器
                self.communication_simulator.set_all_known_flows(all_potential_flows)

                # 启用多次Ring All-Reduce模式
                self.communication_simulator.enable_multiple_allreduces = True

                # 阶段2: 路由优化
                if self.enable_routing_optimizer:
                    self.logger.debug("执行路由优化...")
                    self.routing_optimizer.set_current_time(self.current_time)
                    final_routing_plan = self.routing_optimizer.solve(all_potential_flows)
                else:
                    self.logger.debug("跳过路由优化，使用默认ECMP路径")
                    final_routing_plan = {}
                    for flow in all_potential_flows:
                        flow_id = flow.get("flow_id")
                        if flow.get("candidate_paths"):
                            final_routing_plan[flow_id] = flow["candidate_paths"][0]
                        else:
                            final_routing_plan[flow_id] = []

                # 阶段3: 多次Ring All-Reduce事件驱动模拟
                self.communication_simulator.set_interval_start_time(self.current_time)
                multi_node_communication_times = self.communication_simulator.run(
                    final_routing_plan, multi_node_jobs, interval_duration
                )

                # 保存状态以备跨间隔使用
                self.saved_communication_state = self.communication_simulator.save_simulation_state()

                # 合并多节点作业的平均通信时间结果
                communication_times.update(multi_node_communication_times)
                self.logger.info(f"多次Ring All-Reduce模拟完成: {len(multi_node_communication_times)} 个作业获得平均通信时间")

                # 输出详细统计信息（基于实际完成次数，不再依赖预估）
                for job_name, avg_time in multi_node_communication_times.items():
                    actual_count = self.communication_simulator.allreduce_count.get(job_name, 0)
                    self.logger.info(f"作业 {job_name}: 实际完成 {actual_count} 次Ring All-Reduce，平均通信时间={avg_time:.4f}秒")
            else:
                # 如果多节点作业没有生成流，给予默认通信时间
                for job in multi_node_jobs:
                    communication_times[job.name] = 0.0
                self.logger.warning("多节点作业未生成任何流，使用默认通信时间")
        else:
            self.logger.debug("没有多节点作业，跳过多次Ring All-Reduce模拟")

        return communication_times

    def _handle_cross_interval_communication(self):
        """
        处理跨间隔的通信状态
        """
        # 这个方法在间隔开始时被调用，用于处理跨间隔的状态
        # 实际的状态检查和恢复逻辑已经在_run_high_fidelity_communication_simulation中实现
        pass

    def _save_interval_state(self):
        """
        保存当前间隔结束时的状态
        """
        # 状态保存逻辑已经在_simulate_multiple_allreduces中实现
        # 这里可以添加额外的状态保存逻辑如果需要
        pass

    def _restore_interval_state(self) -> bool:
        """
        恢复上一个间隔的状态

        Returns:
            是否成功恢复状态
        """
        if self.saved_communication_state is None:
            return False

        try:
            self.communication_simulator.load_simulation_state(self.saved_communication_state)
            self.logger.info("成功恢复上一个间隔的通信模拟状态")
            return True
        except Exception as e:
            self.logger.error(f"恢复间隔状态失败: {e}")
            return False

    def autoscale(self, job_infos):
        target_utility = (self.low_util + self.high_util) / 2
        min_nodes = self.min_nodes
        max_nodes = self.max_nodes
        num_nodes = self.num_nodes
        while min_nodes + 1 < max_nodes:
            utility = self.get_utility(num_nodes, job_infos)
            if utility < target_utility:
                max_nodes = num_nodes
            elif utility > target_utility:
                min_nodes = num_nodes
            else:
                break
            num_nodes = (min_nodes + max_nodes) // 2
        min_util = self.get_utility(min_nodes, job_infos)
        max_util = self.get_utility(max_nodes, job_infos)
        if abs(target_utility - min_util) < abs(target_utility - max_util):
            self.num_nodes = min_nodes
        else:
            self.num_nodes = max_nodes

    def get_utility(self, num_nodes, job_infos, allocations=None):
        node_infos = self.get_node_infos(num_nodes)
        if allocations is None:
            policy = copy.deepcopy(self.policy)
            results = self.policy.optimize(job_infos, node_infos,
                                           self.allocations)
            allocations = results[0][1]
        sum_speedup = 0.0
        for key, alloc in allocations.items():
            if key in job_infos:
                speedup_fn = job_infos[key].speedup_fn
                speedup = speedup_fn(len(set(alloc)), len(alloc))
                sum_speedup += speedup
        return sum_speedup / (num_nodes * self.num_gpus)

    def get_job_infos(self):
        job_infos = {}
        for job in self.jobs:
            if self.current_time >= job.submission_time and job.completion_time is None:
                if isinstance(self.policy, TiresiasPolicy):
                    job_infos[job.name] = self.get_tiresias_job_info(job)
                elif isinstance(self.policy, OptimusPolicy):
                    job_infos[job.name] = self.get_optimus_job_info(job)
                else:
                    job_infos[job.name] = self.get_pollux_job_info(job)
        return job_infos

    def get_pollux_job_info(self, job):
        job_info = JobInfo(
            resources={"nvidia.com/gpu": 1},
            speedup_fn=job.get_speedup_fn(),
            creation_timestamp=job.submission_time,
            attained_service=job.attained_service,
            min_replicas=0,
            max_replicas=min(max(2 * job.max_profiled_replicas, 1), 64,  # simulator can't handle more.
                             job.application.max_batch_size // job.application.min_local_bsz),
            preemptible=True,
        )
        if job.application.name == "ncf":
            job_info.max_replicas = 1
        job_info.num_restarts = job.num_restarts or 0
        job_info.age = self.current_time - job.submission_time
        return job_info

    def get_optimus_job_info(self, job):
        job_info = JobInfo(
            resources={"nvidia.com/gpu": 1},
            speedup_fn=job.get_speedup_fn(),
            creation_timestamp=job.submission_time,
            attained_service=job.attained_service,
            min_replicas=0,
            #max_replicas=min(max(2 * job.max_profiled_replicas, 1), 64,  # simulator can't handle more.
            #                 job.target_batch_size // job.application.min_local_bsz),
            max_replicas=(job.target_batch_size // job.application.min_local_bsz),
            preemptible=True,
        )
        if job.application.name == "ncf":
            job_info.max_replicas = 1
        job_info.epoch = job.epoch
        job_info.application = job.application
        job_info.target_batch_size = job.target_batch_size
        return job_info

    def get_tiresias_job_info(self, job):
        return JobInfo(
            resources={"nvidia.com/gpu": 1},
            speedup_fn=None,
            creation_timestamp=job.submission_time,
            attained_service=job.attained_service,
            min_replicas=0,
            max_replicas=job.target_num_replicas,
            preemptible=True,
        )

    def get_node_infos(self, num_nodes=None):
        return {
            idx: NodeInfo({"nvidia.com/gpu": self.num_gpus}, preemptible=False)
            for idx in range(num_nodes or self.num_nodes)
        }

    def all_complete(self):
        return all(job.completion_time is not None for job in self.jobs)

    def output_logs(self, path):
        with open(path, "w") as f:
            for record in self.logs:
                json.dump(record, f)
                f.write("\n")

    def get_jcts(self):
        return {
            val["name"]: val["completion_time"] - val["submission_time"]
            for val in self.logs[-1]["submitted_jobs"]
            if val["completion_time"] is not None
        }


def simulate(args):
    workload = pandas.read_csv(args.workload)
    if args.policy == "tiresias":
        policy = TiresiasPolicy(lambda: simulator.current_time)
    elif args.policy == "optimus":
        policy = OptimusPolicy()
    else:
        policy = PolluxPolicy()
    simulator = Cluster(workload, policy, args.min_nodes, num_gpus=args.num_gpus,
                        max_nodes=args.max_nodes, interference=args.interference,
                        low_util=args.low_util, high_util=args.high_util,
                        enable_routing_optimizer=args.enable_routing_optimizer)
    while not simulator.all_complete():
        simulator.step(args.interval)
        print("---------------- SIMULATOR TIME: {} ----------------"
              .format(simulator.current_time))
        print("Active jobs:")
        for val in simulator.logs[-1]["submitted_jobs"]:
            if val["submission_time"] <= simulator.current_time and val["completion_time"] is None:
                print("    {}:\t[epoch {}]\t[restarts {}]\t[batch size {}]\t[placement {}]".format(
                      val["name"], val["epoch"], val["num_restarts"], val["batch_size"], val["placement"]))
        used_gpus = sum(map(len, simulator.allocations.values()))
        print("GPU utilization: {}".format(used_gpus))
        print("Completed jobs:")
        jct_dict = simulator.get_jcts()
        print(jct_dict)
        print("Average JCT:", sum(jct_dict.values()) / len(jct_dict) if jct_dict else 0)
    if args.output:
        simulator.output_logs(args.output)
    return simulator.logs, simulator.get_jcts()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--workload", type=str,default="simulator/workloads/workload-6.csv",help="path to workload csv")
    parser.add_argument("--policy", type=str, default="pollux",
                        choices=["tiresias", "optimus", "pollux"])
    parser.add_argument("--min-nodes", type=int, default=16,
                        help="min number of nodes in the cluster")
    parser.add_argument("--max-nodes", type=int, default=None,
                        help="max number of nodes for cluster autoscaling")
    parser.add_argument("--interval", type=int, default=60,
                        help="scheduling interval in seconds")
    parser.add_argument("--interference", type=float, default=0.0,
                        help="job slowdown due to interference")
    parser.add_argument("--num-gpus", type=int, default=4,
                        help="number of GPUs per node")
    parser.add_argument("--low-util", type=float,
                        help="low utility threshold")
    parser.add_argument("--high-util", type=float,
                        help="high utility threshold")
    parser.add_argument("--output", type=str,
                        help="path to output logs")
    parser.add_argument("--enable_routing_optimizer", type=int, default=0, help="1表示启用路由优化，0表示禁用")
    args = parser.parse_args()
    if os.path.isdir(args.workload):
        assert args.output is not None and os.path.isdir(args.output)
        args_list = []
        for workload in glob.glob(args.workload + "/*.csv"):
            name = os.path.basename(workload)[:-4]
            args_list.append(copy.deepcopy(args))
            args_list[-1].workload = workload
            args_list[-1].output = args.output + "/" + name + ".log"
        with multiprocessing.Pool(processes=8) as pool:
            ret_list = pool.map(simulate, args_list)
        summary = {"jcts": {}, "avgs": {}}
        for args_item, (_, jct_dict) in zip(args_list, ret_list):
            name = os.path.basename(args_item.workload)[:-4]
            summary["jcts"][name] = jct_dict
            summary["avgs"][name] = sum(jct_dict.values()) / len(jct_dict)
        summary["mean"] = sum(summary["avgs"].values()) / len(summary["avgs"])
        with open(args.output + "/summary.json", "w") as f:
            json.dump(summary, f, indent=4)
    else:
        simulate(args)

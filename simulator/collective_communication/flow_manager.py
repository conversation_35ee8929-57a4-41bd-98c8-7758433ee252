#!/usr/bin/env python3
"""
FlowManager - 高效的流管理器
使用双端队列和直接对象映射优化流存储和操作性能
"""

import logging
import time
from collections import deque
from typing import Dict, List, Optional, Set, Any, Deque

logger = logging.getLogger(__name__)

class FlowManager:
    """
    高效的流管理器
    使用双端队列和分层存储优化性能
    """
    
    def __init__(self):
        """初始化流管理器"""
        # 主存储：双端队列
        self.all_flows: Deque[Dict] = deque()
        
        # 直接对象映射：O(1)查找
        self.flow_id_to_flow: Dict[str, Dict] = {}

        # 流引用计数：追踪流的使用情况
        self.flow_reference_count: Dict[str, int] = {}

        # 流使用状态：精确追踪流的生命周期状态
        self.flow_usage_state: Dict[str, str] = {}  # CREATING, ACTIVE, PROCESSING, COMPLETED, DELETED

        # 分层存储：按状态分类
        self.flows_by_status: Dict[str, Set[str]] = {
            'PLANNED': set(),
            'ACTIVE': set(),
            'COMPLETED': set()
        }
        
        # 按作业分组：优化批量操作
        self.flows_by_job: Dict[str, Set[str]] = {}
        
        # 性能统计
        self.stats = {
            'total_flows': 0,
            'total_additions': 0,
            'total_removals': 0,
            'total_updates': 0,
            'total_lookups': 0,
            'total_batch_operations': 0
        }
        
        logger.debug("FlowManager初始化完成")
    
    def add_flow(self, flow: Dict) -> bool:
        """
        添加单个流
        
        Args:
            flow: 流数据字典
            
        Returns:
            是否添加成功
        """
        flow_id = flow.get('flow_id')
        if not flow_id:
            logger.warning("流缺少flow_id，跳过添加")
            return False
        
        if flow_id in self.flow_id_to_flow:
            logger.debug(f"流 {flow_id} 已存在，跳过添加")
            return False
        
        try:
            # 添加到主存储
            self.all_flows.append(flow)
            
            # 建立直接映射
            self.flow_id_to_flow[flow_id] = flow

            # 初始化引用计数和使用状态
            self.flow_reference_count[flow_id] = 1  # 初始引用计数为1

            # 根据流的状态设置使用状态
            flow_status = flow.get('status', 'PLANNED')
            if flow_status == 'PLANNED':
                self.flow_usage_state[flow_id] = 'ACTIVE'
            elif flow_status == 'ACTIVE':
                self.flow_usage_state[flow_id] = 'PROCESSING'
            elif flow_status == 'COMPLETED':
                self.flow_usage_state[flow_id] = 'COMPLETED'
            else:
                self.flow_usage_state[flow_id] = 'ACTIVE'

            # 分类存储
            status = flow.get('status', 'PLANNED')
            self.flows_by_status[status].add(flow_id)
            
            # 按作业分组
            job_name = flow.get('job_name')
            if job_name:
                if job_name not in self.flows_by_job:
                    self.flows_by_job[job_name] = set()
                self.flows_by_job[job_name].add(flow_id)
            
            # 更新统计
            self.stats['total_flows'] += 1
            self.stats['total_additions'] += 1
            
            logger.debug(f"成功添加流 {flow_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加流 {flow_id} 时出错: {e}")
            return False
    
    def add_flows_batch(self, flows: List[Dict]) -> int:
        """
        批量添加流
        
        Args:
            flows: 流数据列表
            
        Returns:
            成功添加的流数量
        """
        if not flows:
            return 0
        
        start_time = time.time()
        added_count = 0
        
        try:
            for flow in flows:
                if self.add_flow(flow):
                    added_count += 1
            
            # 更新批量操作统计
            self.stats['total_batch_operations'] += 1
            duration = time.time() - start_time
            
            logger.info(f"批量添加完成：{added_count}/{len(flows)} 个流，耗时 {duration*1000:.2f}ms")
            return added_count
            
        except Exception as e:
            logger.error(f"批量添加流时出错: {e}")
            return added_count
    
    def get_flow_by_id(self, flow_id: str) -> Optional[Dict]:
        """
        通过ID获取流（O(1)查找）
        
        Args:
            flow_id: 流ID
            
        Returns:
            流数据字典，如果不存在则返回None
        """
        self.stats['total_lookups'] += 1
        return self.flow_id_to_flow.get(flow_id)
    
    def update_flow_status(self, flow_id: str, new_status: str) -> bool:
        """
        更新流状态
        
        Args:
            flow_id: 流ID
            new_status: 新状态
            
        Returns:
            是否更新成功
        """
        flow = self.get_flow_by_id(flow_id)
        if not flow:
            logger.warning(f"流 {flow_id} 不存在，无法更新状态")
            return False
        
        try:
            old_status = flow.get('status', 'PLANNED')
            
            # 更新流对象
            flow['status'] = new_status
            
            # 更新分类存储
            if old_status in self.flows_by_status:
                self.flows_by_status[old_status].discard(flow_id)
            
            if new_status not in self.flows_by_status:
                self.flows_by_status[new_status] = set()
            self.flows_by_status[new_status].add(flow_id)
            
            # 更新统计
            self.stats['total_updates'] += 1
            
            logger.debug(f"流 {flow_id} 状态更新: {old_status} -> {new_status}")
            return True
            
        except Exception as e:
            logger.error(f"更新流 {flow_id} 状态时出错: {e}")
            return False
    
    def get_flows_by_status(self, status: str) -> List[Dict]:
        """
        获取指定状态的所有流
        
        Args:
            status: 流状态
            
        Returns:
            流数据列表
        """
        if status not in self.flows_by_status:
            return []
        
        flows = []
        for flow_id in self.flows_by_status[status]:
            flow = self.flow_id_to_flow.get(flow_id)
            if flow:
                flows.append(flow)
        
        return flows
    
    def get_flows_by_job(self, job_name: str) -> List[Dict]:
        """
        获取指定作业的所有流
        
        Args:
            job_name: 作业名称
            
        Returns:
            流数据列表
        """
        if job_name not in self.flows_by_job:
            return []
        
        flows = []
        for flow_id in self.flows_by_job[job_name]:
            flow = self.flow_id_to_flow.get(flow_id)
            if flow:
                flows.append(flow)
        
        return flows
    
    def get_all_flows(self) -> List[Dict]:
        """
        获取所有流的列表（保持与原接口兼容）
        
        Returns:
            所有流的列表
        """
        return list(self.all_flows)
    
    def get_flow_count(self) -> int:
        """
        获取流总数
        
        Returns:
            流总数
        """
        return len(self.all_flows)
    
    def clear(self):
        """清空所有流数据"""
        self.all_flows.clear()
        self.flow_id_to_flow.clear()

        # 清空引用计数和使用状态
        self.flow_reference_count.clear()
        self.flow_usage_state.clear()

        self.flows_by_status = {
            'PLANNED': set(),
            'ACTIVE': set(),
            'COMPLETED': set()
        }
        self.flows_by_job.clear()

        # 重置统计（保留累积统计）
        self.stats['total_flows'] = 0

        logger.debug("FlowManager已清空")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'current_flows': len(self.all_flows),
            'flows_by_status': {
                status: len(flow_ids)
                for status, flow_ids in self.flows_by_status.items()
            },
            'flows_by_job_count': len(self.flows_by_job),
            'total_references': sum(self.flow_reference_count.values()),
            'flows_with_references': len([c for c in self.flow_reference_count.values() if c > 0]),
            'usage_states': {
                state: len([s for s in self.flow_usage_state.values() if s == state])
                for state in ['CREATING', 'ACTIVE', 'PROCESSING', 'COMPLETED', 'DELETING', 'DELETED']
            },
            'performance_stats': self.stats.copy()
        }

    def batch_remove_flows(self, job_name: str, target_round: int, target_status: str = 'COMPLETED') -> int:
        """
        批量删除指定条件的流（高效双端队列实现）

        Args:
            job_name: 作业名称
            target_round: 目标轮次
            target_status: 目标状态

        Returns:
            删除的流数量
        """
        if not self.all_flows:
            return 0

        start_time = time.time()
        flows_before = len(self.all_flows)

        try:
            # 找出要删除的流ID（增加安全检查）
            flows_to_remove = set()
            flows_with_references = []

            for flow in self.all_flows:
                if (flow.get('job_name') == job_name and
                    flow.get('round_idx') == target_round and
                    flow.get('status') == target_status):

                    flow_id = flow['flow_id']

                    # 检查引用计数
                    ref_count = self.get_flow_reference_count(flow_id)
                    usage_state = self.get_flow_usage_state(flow_id)

                    # 如果引用计数大于1（除了初始引用），则不删除
                    if ref_count > 1:
                        flows_with_references.append((flow_id, ref_count, usage_state))
                        logger.debug(f"流 {flow_id} 有 {ref_count} 个引用，暂不删除")
                    else:
                        flows_to_remove.add(flow_id)
                        # 标记为删除中状态
                        self.set_flow_usage_state(flow_id, 'DELETING')

            if not flows_to_remove:
                if flows_with_references:
                    logger.info(f"作业 {job_name} 轮次 {target_round} 有 {len(flows_with_references)} 个流因有引用而暂不删除")
                return 0

            # 高效过滤：创建新的双端队列
            new_flows = deque()
            removed_count = 0

            for flow in self.all_flows:
                flow_id = flow['flow_id']
                if flow_id not in flows_to_remove:
                    new_flows.append(flow)
                else:
                    # 从映射中删除
                    self.flow_id_to_flow.pop(flow_id, None)

                    # 清理引用计数和使用状态
                    self.flow_reference_count.pop(flow_id, None)
                    self.flow_usage_state.pop(flow_id, None)

                    # 从分类存储中删除
                    status = flow.get('status', 'PLANNED')
                    if status in self.flows_by_status:
                        self.flows_by_status[status].discard(flow_id)

                    # 从作业分组中删除
                    job = flow.get('job_name')
                    if job and job in self.flows_by_job:
                        self.flows_by_job[job].discard(flow_id)
                        # 如果作业没有流了，删除作业分组
                        if not self.flows_by_job[job]:
                            del self.flows_by_job[job]

                    removed_count += 1

            # 替换主存储
            self.all_flows = new_flows

            # 更新统计
            self.stats['total_flows'] -= removed_count
            self.stats['total_removals'] += removed_count
            self.stats['total_batch_operations'] += 1

            duration = time.time() - start_time
            flows_after = len(self.all_flows)

            logger.debug(f"批量删除完成：删除了 {removed_count} 个流，剩余 {flows_after} 个流，耗时 {duration*1000:.2f}ms")
            return removed_count

        except Exception as e:
            logger.error(f"批量删除流时出错: {e}")
            return 0

    def remove_flow_by_id(self, flow_id: str) -> bool:
        """
        删除单个流

        Args:
            flow_id: 流ID

        Returns:
            是否删除成功
        """
        flow = self.get_flow_by_id(flow_id)
        if not flow:
            return False

        try:
            # 从主存储中删除（需要遍历）
            new_flows = deque()
            found = False

            for f in self.all_flows:
                if f['flow_id'] != flow_id:
                    new_flows.append(f)
                else:
                    found = True

            if not found:
                return False

            self.all_flows = new_flows

            # 从映射中删除
            del self.flow_id_to_flow[flow_id]

            # 从分类存储中删除
            status = flow.get('status', 'PLANNED')
            if status in self.flows_by_status:
                self.flows_by_status[status].discard(flow_id)

            # 从作业分组中删除
            job_name = flow.get('job_name')
            if job_name and job_name in self.flows_by_job:
                self.flows_by_job[job_name].discard(flow_id)
                if not self.flows_by_job[job_name]:
                    del self.flows_by_job[job_name]

            # 更新统计
            self.stats['total_flows'] -= 1
            self.stats['total_removals'] += 1

            logger.debug(f"成功删除流 {flow_id}")
            return True

        except Exception as e:
            logger.error(f"删除流 {flow_id} 时出错: {e}")
            return False

    def get_active_flows_at_time(self, target_time: float) -> List[Dict]:
        """
        获取指定时间的活跃流（优化版本）

        Args:
            target_time: 目标时间

        Returns:
            活跃流列表
        """
        active_flows = []

        # 优先检查ACTIVE状态的流
        for flow_id in self.flows_by_status.get('ACTIVE', set()):
            flow = self.flow_id_to_flow.get(flow_id)
            if not flow:
                continue

            start_time = flow.get('start_time', 0.0)

            # 使用预测FCT判断结束时间
            if 'predicted_fct' in flow:
                end_time = start_time + flow['predicted_fct']
                if start_time <= target_time < end_time:
                    active_flows.append(flow)

        # 检查PLANNED状态的流（可能有预测FCT）
        for flow_id in self.flows_by_status.get('PLANNED', set()):
            flow = self.flow_id_to_flow.get(flow_id)
            if not flow or 'predicted_fct' not in flow:
                continue

            start_time = flow.get('start_time', 0.0)
            end_time = start_time + flow['predicted_fct']

            if start_time <= target_time < end_time:
                active_flows.append(flow)

        return active_flows

    def get_flows_in_time_window(self, start_time: float, end_time: float) -> List[Dict]:
        """
        获取指定时间窗口内的流

        Args:
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            时间窗口内的流列表
        """
        flows_in_window = []

        for flow in self.all_flows:
            flow_start = flow.get('start_time', 0.0)

            # 检查流是否在时间窗口内
            if 'predicted_fct' in flow:
                flow_end = flow_start + flow['predicted_fct']
                # 流与时间窗口有重叠
                if not (flow_end <= start_time or flow_start >= end_time):
                    flows_in_window.append(flow)
            elif flow_start >= start_time and flow_start <= end_time:
                flows_in_window.append(flow)

        return flows_in_window

    def get_synchronous_flows(self, target_flow: Dict) -> List[Dict]:
        """
        获取与目标流同步开始的流

        Args:
            target_flow: 目标流

        Returns:
            同步流列表
        """
        target_start_time = target_flow.get('start_time', 0.0)
        target_job = target_flow.get('job_name')
        synchronous_flows = []

        # 优化：如果是同一作业，优先在作业分组中查找
        if target_job and target_job in self.flows_by_job:
            for flow_id in self.flows_by_job[target_job]:
                flow = self.flow_id_to_flow.get(flow_id)
                if (flow and
                    flow is not target_flow and
                    flow.get('start_time', 0.0) == target_start_time):
                    synchronous_flows.append(flow)
        else:
            # 全局查找
            for flow in self.all_flows:
                if (flow is not target_flow and
                    flow.get('start_time', 0.0) == target_start_time):
                    synchronous_flows.append(flow)

        return synchronous_flows

    def update_flow_field(self, flow_id: str, field_name: str, field_value: Any) -> bool:
        """
        更新流的指定字段

        Args:
            flow_id: 流ID
            field_name: 字段名
            field_value: 字段值

        Returns:
            是否更新成功
        """
        flow = self.get_flow_by_id(flow_id)
        if not flow:
            return False

        try:
            flow[field_name] = field_value
            self.stats['total_updates'] += 1
            return True
        except Exception as e:
            logger.error(f"更新流 {flow_id} 字段 {field_name} 时出错: {e}")
            return False

    def acquire_flow_reference(self, flow_id: str) -> bool:
        """
        获取流的引用（增加引用计数）

        Args:
            flow_id: 流ID

        Returns:
            是否成功获取引用
        """
        if flow_id not in self.flow_id_to_flow:
            return False

        # 检查流是否可以被引用
        usage_state = self.flow_usage_state.get(flow_id, 'UNKNOWN')
        if usage_state in ['DELETED', 'DELETING']:
            logger.debug(f"流 {flow_id} 已删除或正在删除，无法获取引用")
            return False

        # 增加引用计数
        self.flow_reference_count[flow_id] = self.flow_reference_count.get(flow_id, 0) + 1
        logger.debug(f"流 {flow_id} 引用计数增加到 {self.flow_reference_count[flow_id]}")
        return True

    def release_flow_reference(self, flow_id: str) -> int:
        """
        释放流的引用（减少引用计数）

        Args:
            flow_id: 流ID

        Returns:
            当前引用计数
        """
        if flow_id not in self.flow_reference_count:
            return 0

        # 减少引用计数
        self.flow_reference_count[flow_id] = max(0, self.flow_reference_count[flow_id] - 1)
        current_count = self.flow_reference_count[flow_id]

        logger.debug(f"流 {flow_id} 引用计数减少到 {current_count}")

        # 如果引用计数为0且状态为COMPLETED，可以考虑删除
        if current_count == 0:
            usage_state = self.flow_usage_state.get(flow_id, 'UNKNOWN')
            if usage_state == 'COMPLETED':
                logger.debug(f"流 {flow_id} 引用计数为0且已完成，可以安全删除")

        return current_count

    def get_flow_reference_count(self, flow_id: str) -> int:
        """
        获取流的当前引用计数

        Args:
            flow_id: 流ID

        Returns:
            引用计数
        """
        return self.flow_reference_count.get(flow_id, 0)

    def set_flow_usage_state(self, flow_id: str, new_state: str) -> bool:
        """
        设置流的使用状态

        Args:
            flow_id: 流ID
            new_state: 新状态 (CREATING, ACTIVE, PROCESSING, COMPLETED, DELETING, DELETED)

        Returns:
            是否设置成功
        """
        if flow_id not in self.flow_id_to_flow:
            return False

        old_state = self.flow_usage_state.get(flow_id, 'UNKNOWN')
        self.flow_usage_state[flow_id] = new_state

        logger.debug(f"流 {flow_id} 使用状态变更: {old_state} -> {new_state}")
        return True

    def get_flow_usage_state(self, flow_id: str) -> str:
        """
        获取流的使用状态

        Args:
            flow_id: 流ID

        Returns:
            使用状态
        """
        return self.flow_usage_state.get(flow_id, 'UNKNOWN')

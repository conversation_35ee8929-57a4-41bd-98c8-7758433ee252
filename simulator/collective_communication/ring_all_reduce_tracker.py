#!/usr/bin/env python3
"""
Ring All-Reduce追踪器 - 追踪每个作业的All-Reduce状态
管理轮次进度、计算下一轮开始时间、触发新轮次流生成
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
from enum import Enum

logger = logging.getLogger(__name__)

class JobStatus(Enum):
    """作业通信状态枚举"""
    NOT_STARTED = "NOT_STARTED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"

class RoundStatus(Enum):
    """轮次状态枚举"""
    PENDING = "PENDING"      # 等待开始
    ACTIVE = "ACTIVE"        # 正在进行
    COMPLETED = "COMPLETED"  # 已完成

class RingAllReduceTracker:
    """Ring All-Reduce追踪器，追踪每个作业的通信状态"""
    
    def __init__(self, flow_generator, topology_manager):
        """
        初始化追踪器
        
        Args:
            flow_generator: 流生成器实例
            topology_manager: 拓扑管理器实例
        """
        self.flow_generator = flow_generator
        self.topology_manager = topology_manager
        
        # 作业状态追踪
        self.job_status = {}          # {job_name: JobStatus}
        self.job_ring_sizes = {}      # {job_name: ring_size}
        self.job_total_rounds = {}    # {job_name: total_rounds}
        self.job_current_rounds = {} # {job_name: current_round}
        self.job_round_status = {}    # {job_name: {round_idx: RoundStatus}}
        self.job_round_flows = {}     # {job_name: {round_idx: [flow_ids]}}
        self.job_round_start_times = {} # {job_name: {round_idx: start_time}}
        self.job_round_end_times = {}   # {job_name: {round_idx: end_time}}
        self.job_communication_times = {} # {job_name: total_communication_time}
        
        # 流状态追踪
        self.flow_to_job = {}         # {flow_id: job_name}
        self.flow_to_round = {}       # {flow_id: round_idx}
        self.active_flows = {}        # {flow_id: flow_data}
        self.completed_flows = {}     # {flow_id: completion_time}
        
        logger.info("Ring All-Reduce追踪器初始化完成")
    
    def initialize_job(self, job, ring_size: int, first_round_flows: List[Dict]):
        """
        初始化作业追踪
        
        Args:
            job: 作业对象
            ring_size: Ring大小
            first_round_flows: 第一轮流列表
        """
        job_name = job.name
        total_rounds = 2 * (ring_size - 1) if ring_size > 1 else 0
        
        # 初始化作业状态
        self.job_status[job_name] = JobStatus.NOT_STARTED
        self.job_ring_sizes[job_name] = ring_size
        self.job_total_rounds[job_name] = total_rounds
        self.job_current_rounds[job_name] = -1  # 尚未开始
        self.job_round_status[job_name] = {}
        self.job_round_flows[job_name] = {}
        self.job_round_start_times[job_name] = {}
        self.job_round_end_times[job_name] = {}
        self.job_communication_times[job_name] = 0.0
        
        # 初始化所有轮次状态
        for round_idx in range(total_rounds):
            self.job_round_status[job_name][round_idx] = RoundStatus.PENDING
            self.job_round_flows[job_name][round_idx] = []
        
        # 注册第一轮流
        if first_round_flows:
            self._register_round_flows(job_name, 0, first_round_flows)
        
        logger.debug(f"初始化作业 {job_name}: Ring大小={ring_size}, 总轮次={total_rounds}")
    
    def start_job_communication(self, job_name: str, start_time: float):
        """
        开始作业通信
        
        Args:
            job_name: 作业名称
            start_time: 开始时间
        """
        if job_name not in self.job_status:
            logger.warning(f"作业 {job_name} 未初始化")
            return
        
        self.job_status[job_name] = JobStatus.IN_PROGRESS
        self.job_current_rounds[job_name] = 0
        
        # 标记第一轮为活跃状态
        if 0 in self.job_round_status[job_name]:
            self.job_round_status[job_name][0] = RoundStatus.ACTIVE
            self.job_round_start_times[job_name][0] = start_time
            logger.debug(f"作业 {job_name} 第一轮开始时间已记录: {start_time:.4f}")
        else:
            logger.warning(f"作业 {job_name} 第一轮状态不存在，无法记录开始时间")
        
        logger.debug(f"作业 {job_name} 开始通信，时间={start_time:.4f}")
    
    def _register_round_flows(self, job_name: str, round_idx: int, flows: List[Dict]):
        """
        注册轮次的流
        
        Args:
            job_name: 作业名称
            round_idx: 轮次索引
            flows: 流列表
        """
        flow_ids = []
        
        for flow in flows:
            flow_id = flow["flow_id"]
            flow_ids.append(flow_id)
            
            # 建立映射关系
            self.flow_to_job[flow_id] = job_name
            self.flow_to_round[flow_id] = round_idx
        
        self.job_round_flows[job_name][round_idx] = flow_ids
        logger.debug(f"注册作业 {job_name} 轮次 {round_idx} 的 {len(flow_ids)} 个流")
    
    def start_flow(self, flow_id: str, flow_data: Dict, start_time: float):
        """
        标记流开始
        
        Args:
            flow_id: 流ID
            flow_data: 流数据
            start_time: 开始时间
        """
        self.active_flows[flow_id] = flow_data
        
        # 更新流的开始时间
        flow_data["start_time"] = start_time
        flow_data["status"] = "ACTIVE"
        
        logger.debug(f"流 {flow_id} 开始传输，时间={start_time:.4f}")
    
    def finish_flow(self, flow_id: str, end_time: float) -> Optional[List[Dict]]:
        """
        标记流完成，检查是否触发新轮次
        
        Args:
            flow_id: 流ID
            end_time: 完成时间
            
        Returns:
            如果触发新轮次，返回新轮次的流列表；否则返回None
        """
        if flow_id not in self.flow_to_job:
            logger.warning(f"未知流 {flow_id}")
            return None
        
        job_name = self.flow_to_job[flow_id]
        round_idx = self.flow_to_round[flow_id]
        
        # 移动流从活跃到完成状态
        if flow_id in self.active_flows:
            flow_data = self.active_flows.pop(flow_id)
            flow_data["status"] = "COMPLETED"
            self.completed_flows[flow_id] = end_time
        
        logger.debug(f"流 {flow_id} 完成，时间={end_time:.4f}")
        
        # 检查该轮次是否完成
        if self._is_round_completed(job_name, round_idx):
            return self._complete_round(job_name, round_idx, end_time)
        
        return None
    
    def _is_round_completed(self, job_name: str, round_idx: int) -> bool:
        """
        检查轮次是否完成（该轮次的所有流都已完成）
        
        Args:
            job_name: 作业名称
            round_idx: 轮次索引
            
        Returns:
            轮次是否完成
        """
        if job_name not in self.job_round_flows:
            return False
        
        round_flow_ids = self.job_round_flows[job_name].get(round_idx, [])
        
        # 检查该轮次的所有流是否都已完成
        for flow_id in round_flow_ids:
            if flow_id in self.active_flows:
                return False  # 还有活跃流
        
        return True
    
    def _complete_round(self, job_name: str, round_idx: int, end_time: float) -> Optional[List[Dict]]:
        """
        完成轮次，并尝试启动下一轮次
        
        Args:
            job_name: 作业名称
            round_idx: 完成的轮次索引
            end_time: 完成时间
            
        Returns:
            下一轮次的流列表，如果没有下一轮次则返回None
        """
        # 标记轮次完成
        self.job_round_status[job_name][round_idx] = RoundStatus.COMPLETED
        self.job_round_end_times[job_name][round_idx] = end_time
        
        logger.debug(f"作业 {job_name} 轮次 {round_idx} 完成，时间={end_time:.4f}")
        
        # 检查是否为最后一轮
        total_rounds = self.job_total_rounds[job_name]
        next_round = round_idx + 1
        
        logger.debug(f"作业 {job_name}: 当前轮次={round_idx}, 下一轮次={next_round}, 总轮次={total_rounds}")
        
        if next_round >= total_rounds:
            # 所有轮次完成，作业通信结束
            logger.debug(f"作业 {job_name} 所有 {total_rounds} 轮通信完成，开始计算总通信时间")
            self._complete_job_communication(job_name, end_time)
            return None
        
        # 启动下一轮次
        return self._start_next_round(job_name, next_round, end_time)
    
    def _complete_job_communication(self, job_name: str, end_time: float) -> None:
        """
        完成作业的所有通信
        
        Args:
            job_name: 作业名称
            end_time: 完成时间
        """
        self.job_status[job_name] = JobStatus.COMPLETED
        
        # 调试：查看所有轮次的开始时间
        logger.debug(f"作业 {job_name} 轮次开始时间记录: {self.job_round_start_times[job_name]}")
        
        # 计算总通信时间 - 增强版本，多种备选方案
        total_comm_time = 0.0
        calculation_method = "unknown"
        
        # 方案1：使用第一轮记录的开始时间（标准方法）
        if job_name in self.job_round_start_times and 0 in self.job_round_start_times[job_name]:
            start_time = self.job_round_start_times[job_name][0]
            total_comm_time = end_time - start_time
            calculation_method = "第一轮开始时间"
            logger.debug(f"作业 {job_name} 通信完成，总时间={total_comm_time:.4f}秒 (开始:{start_time:.4f}, 结束:{end_time:.4f}) [方法: {calculation_method}]")
        
        # 方案2：从活跃流的开始时间推导
        elif self.active_flows or self.completed_flows:
            job_flows = [f for f, j in self.flow_to_job.items() if j == job_name]
            if job_flows:
                # 查找该作业最早的流开始时间
                earliest_start = float('inf')
                for flow_id in job_flows:
                    if flow_id in self.active_flows:
                        flow_start = self.active_flows[flow_id].get("start_time", float('inf'))
                        earliest_start = min(earliest_start, flow_start)
                    elif flow_id in self.completed_flows:
                        # 对于已完成的流，我们需要从流数据中获取开始时间
                        # 这需要额外的记录机制
                        pass
                
                if earliest_start < float('inf'):
                    total_comm_time = end_time - earliest_start
                    calculation_method = "流开始时间推导"
                    logger.info(f"作业 {job_name} 通过流开始时间计算通信时间={total_comm_time:.4f}秒 [方法: {calculation_method}]")
                else:
                    logger.warning(f"作业 {job_name} 无法找到有效的流开始时间")
            else:
                logger.warning(f"作业 {job_name} 没有找到相关流")
        
        # 方案3：从其他轮次开始时间反推
        elif job_name in self.job_round_start_times and self.job_round_start_times[job_name]:
            # 找到最早的轮次开始时间
            earliest_round_start = min(self.job_round_start_times[job_name].values())
            total_comm_time = end_time - earliest_round_start
            calculation_method = "最早轮次时间"
            logger.info(f"作业 {job_name} 通过最早轮次时间计算通信时间={total_comm_time:.4f}秒 [方法: {calculation_method}]")
        
        else:
            logger.warning(f"作业 {job_name} 所有计算方法都失败，设置通信时间为0")
            total_comm_time = 0.0
            calculation_method = "失败"
        
        self.job_communication_times[job_name] = total_comm_time
        
        return None
    
    def _start_next_round(self, job_name: str, next_round: int, start_time: float) -> List[Dict]:
        """
        启动下一轮次
        
        Args:
            job_name: 作业名称
            next_round: 下一轮次索引
            start_time: 开始时间
            
        Returns:
            下一轮次的流列表
        """
        # 更新当前轮次
        self.job_current_rounds[job_name] = next_round
        self.job_round_status[job_name][next_round] = RoundStatus.ACTIVE
        self.job_round_start_times[job_name][next_round] = start_time
        
        logger.debug(f"作业 {job_name} 启动轮次 {next_round}，时间={start_time:.4f}")
        
        # 注意：这里返回空列表，流生成由CommunicationSimulator中的逻辑处理
        # 这确保了追踪器不负责流生成，只负责状态管理
        return []
    
    def get_current_round(self, job_name: str) -> int:
        """
        获取作业当前轮次
        
        Args:
            job_name: 作业名称
            
        Returns:
            当前轮次索引
        """
        return self.job_current_rounds.get(job_name, -1)
    
    def generate_next_round_flows(self, job, current_round: int) -> List[Dict]:
        """
        为作业生成下一轮的流
        
        Args:
            job: 作业对象
            current_round: 当前完成的轮次
            
        Returns:
            下一轮的流列表
        """
        next_round_flows = self.flow_generator.generate_next_round_flows(
            job, current_round, self.topology_manager
        )
        
        if next_round_flows:
            next_round = current_round + 1
            self._register_round_flows(job.name, next_round, next_round_flows)
        
        return next_round_flows
    
    def get_job_status(self, job_name: str) -> JobStatus:
        """
        获取作业状态
        
        Args:
            job_name: 作业名称
            
        Returns:
            作业状态
        """
        return self.job_status.get(job_name, JobStatus.NOT_STARTED)
    
    def get_job_communication_time(self, job_name: str) -> float:
        """
        获取作业的总通信时间
        
        Args:
            job_name: 作业名称
            
        Returns:
            总通信时间（秒）
        """
        return self.job_communication_times.get(job_name, 0.0)
    
    def get_job_progress(self, job_name: str) -> Dict[str, Any]:
        """
        获取作业的通信进度
        
        Args:
            job_name: 作业名称
            
        Returns:
            进度信息
        """
        if job_name not in self.job_status:
            return {"status": "NOT_FOUND"}
        
        current_round = self.job_current_rounds.get(job_name, -1)
        total_rounds = self.job_total_rounds.get(job_name, 0)
        
        completed_rounds = 0
        for round_idx, status in self.job_round_status.get(job_name, {}).items():
            if status == RoundStatus.COMPLETED:
                completed_rounds += 1
        
        progress = completed_rounds / total_rounds if total_rounds > 0 else 0.0
        
        return {
            "status": self.job_status[job_name].value,
            "current_round": current_round,
            "total_rounds": total_rounds,
            "completed_rounds": completed_rounds,
            "progress": progress,
            "ring_size": self.job_ring_sizes.get(job_name, 0),
            "communication_time": self.job_communication_times.get(job_name, 0.0)
        }
    
    def get_active_flows(self) -> Dict[str, Dict]:
        """
        获取所有活跃流
        
        Returns:
            活跃流字典 {flow_id: flow_data}
        """
        return self.active_flows.copy()
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取追踪器统计信息
        
        Returns:
            统计信息
        """
        total_jobs = len(self.job_status)
        status_counts = defaultdict(int)
        
        for status in self.job_status.values():
            status_counts[status.value] += 1
        
        total_flows = len(self.flow_to_job)
        active_flows_count = len(self.active_flows)
        completed_flows_count = len(self.completed_flows)
        
        return {
            "total_jobs": total_jobs,
            "job_status_distribution": dict(status_counts),
            "total_flows": total_flows,
            "active_flows": active_flows_count,
            "completed_flows": completed_flows_count,
            "avg_communication_time": (
                sum(self.job_communication_times.values()) / 
                len([t for t in self.job_communication_times.values() if t > 0])
                if any(t > 0 for t in self.job_communication_times.values()) else 0.0
            )
        }
    
    def save_state(self) -> Dict[str, Any]:
        """
        保存追踪器状态
        
        Returns:
            状态字典
        """
        # 将枚举值转换为字符串以便序列化
        serialized_job_status = {k: v.value for k, v in self.job_status.items()}
        serialized_round_status = {}
        
        for job_name, rounds in self.job_round_status.items():
            serialized_round_status[job_name] = {
                str(round_idx): status.value for round_idx, status in rounds.items()
            }
        
        return {
            "job_status": serialized_job_status,
            "job_ring_sizes": self.job_ring_sizes,
            "job_total_rounds": self.job_total_rounds,
            "job_current_rounds": self.job_current_rounds,
            "job_round_status": serialized_round_status,
            "job_round_flows": self.job_round_flows,
            "job_round_start_times": self.job_round_start_times,
            "job_round_end_times": self.job_round_end_times,
            "job_communication_times": self.job_communication_times,
            "flow_to_job": self.flow_to_job,
            "flow_to_round": self.flow_to_round,
            "completed_flows": self.completed_flows
        }
    
    def load_state(self, state: Dict[str, Any]):
        """
        从状态字典恢复追踪器状态
        
        Args:
            state: 状态字典
        """
        # 恢复作业状态（转换回枚举）
        self.job_status = {k: JobStatus(v) for k, v in state.get("job_status", {}).items()}
        
        self.job_ring_sizes = state.get("job_ring_sizes", {})
        self.job_total_rounds = state.get("job_total_rounds", {})
        self.job_current_rounds = state.get("job_current_rounds", {})
        
        # 恢复轮次状态（转换回枚举）
        self.job_round_status = {}
        for job_name, rounds in state.get("job_round_status", {}).items():
            self.job_round_status[job_name] = {
                int(round_idx): RoundStatus(status) for round_idx, status in rounds.items()
            }
        
        self.job_round_flows = state.get("job_round_flows", {})
        self.job_round_start_times = state.get("job_round_start_times", {})
        self.job_round_end_times = state.get("job_round_end_times", {})
        self.job_communication_times = state.get("job_communication_times", {})
        self.flow_to_job = state.get("flow_to_job", {})
        self.flow_to_round = state.get("flow_to_round", {})
        self.completed_flows = state.get("completed_flows", {})
        
        # 活跃流不需要恢复，因为它们会在模拟继续时重新建立
        self.active_flows = {}
        
        logger.info(f"恢复追踪器状态: {len(self.job_status)} 个作业")
    
    def reset(self):
        """重置追踪器到初始状态"""
        self.job_status.clear()
        self.job_ring_sizes.clear()
        self.job_total_rounds.clear()
        self.job_current_rounds.clear()
        self.job_round_status.clear()
        self.job_round_flows.clear()
        self.job_round_start_times.clear()
        self.job_round_end_times.clear()
        self.job_communication_times.clear()
        self.flow_to_job.clear()
        self.flow_to_round.clear()
        self.active_flows.clear()
        self.completed_flows.clear()
        
        logger.debug("All-Reduce追踪器已重置")

    def reset_job_state(self, job_name: str) -> None:
        """
        重置单个作业的状态，用于多次Ring All-Reduce（保守版本）

        Args:
            job_name: 作业名称
        """
        if job_name in self.job_status:
            # 保留基本配置，重置状态
            ring_size = self.job_ring_sizes.get(job_name, 1)
            total_rounds = self.job_total_rounds.get(job_name, 0)

            # 🔧 保守检查：确认没有活跃流再清理映射
            active_flows_for_job = [flow_id for flow_id, flow_data in self.active_flows.items() 
                                  if flow_data.get('job_name') == job_name]
            
            if active_flows_for_job:
                logger.warning(f"⚠️  作业 {job_name} 重置时发现 {len(active_flows_for_job)} 个活跃流，延迟重置")
                # 不立即清理映射，而是只重置状态字段
                self.job_status[job_name] = JobStatus.NOT_STARTED
                self.job_current_rounds[job_name] = -1
                self.job_communication_times[job_name] = 0.0
                return

            # 重置状态字段
            self.job_status[job_name] = JobStatus.NOT_STARTED
            self.job_current_rounds[job_name] = -1
            self.job_communication_times[job_name] = 0.0

            # 重置轮次状态
            self.job_round_status[job_name] = {}
            self.job_round_flows[job_name] = {}
            self.job_round_start_times[job_name] = {}
            self.job_round_end_times[job_name] = {}

            # 初始化所有轮次状态
            for round_idx in range(total_rounds):
                self.job_round_status[job_name][round_idx] = RoundStatus.PENDING
                self.job_round_flows[job_name][round_idx] = []

            # 🔧 安全清理：只清理已完成流的映射，保留可能仍在处理的流映射
            flows_to_remove = []
            for flow_id, job in self.flow_to_job.items():
                if job == job_name:
                    # 只清理明确已完成的流
                    if flow_id in self.completed_flows:
                        flows_to_remove.append(flow_id)
                    else:
                        logger.debug(f"保留流 {flow_id} 的映射（可能仍在处理中）")
            
            for flow_id in flows_to_remove:
                self.flow_to_job.pop(flow_id, None)
                self.flow_to_round.pop(flow_id, None)
                self.completed_flows.pop(flow_id, None)

            logger.debug(f"作业 {job_name} 状态已安全重置，Ring大小={ring_size}，总轮次={total_rounds}，清理了{len(flows_to_remove)}个已完成流")
        else:
            logger.warning(f"尝试重置未知作业 {job_name} 的状态")
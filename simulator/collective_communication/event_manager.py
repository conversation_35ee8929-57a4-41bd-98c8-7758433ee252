#!/usr/bin/env python3
"""
事件管理器 - 管理事件驱动模拟的事件优先队列
支持START_FLOW和FINISH_FLOW事件类型
"""

import heapq
import logging
from typing import List, Tuple, Any, Dict, Optional
from enum import Enum
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)

class EventType(Enum):
    """事件类型枚举"""
    START_FLOW = "START_FLOW"
    FINISH_FLOW = "FINISH_FLOW"
    RETRY_NEW_CYCLE = "RETRY_NEW_CYCLE"

@dataclass
class Event:
    """事件数据结构"""
    timestamp: float
    event_type: EventType
    data: Dict[str, Any] = field(default_factory=dict)
    
    def __lt__(self, other):
        """支持优先队列排序，按时间戳升序"""
        return self.timestamp < other.timestamp
    
    def __le__(self, other):
        return self.timestamp <= other.timestamp
    
    def __gt__(self, other):
        return self.timestamp > other.timestamp
    
    def __ge__(self, other):
        return self.timestamp >= other.timestamp
    
    def __eq__(self, other):
        return self.timestamp == other.timestamp

class EventManager:
    """事件管理器，维护事件优先队列"""
    
    def __init__(self):
        """初始化事件管理器"""
        self.event_queue = []  # 优先队列（最小堆）
        self.current_time = 0.0
        self.processed_events = 0
        self.total_events_added = 0
        
        logger.info("事件管理器初始化完成")
    
    def add_event(self, timestamp: float, event_type: EventType, data: Dict[str, Any] = None):
        """
        添加事件到队列
        
        Args:
            timestamp: 事件时间戳
            event_type: 事件类型
            data: 事件数据
        """
        if data is None:
            data = {}
        
        event = Event(timestamp, event_type, data)
        heapq.heappush(self.event_queue, event)
        self.total_events_added += 1
        
        logger.debug(f"添加事件: {event_type.value} at {timestamp:.4f}")
    
    def add_start_flow_event(self, timestamp: float, flow: Dict[str, Any]):
        """
        添加流开始事件
        
        Args:
            timestamp: 开始时间
            flow: 流数据
        """
        data = {
            "flow": flow,
            "flow_id": flow.get("flow_id", "unknown")
        }
        self.add_event(timestamp, EventType.START_FLOW, data)
    
    def add_finish_flow_event(self, timestamp: float, flow: Dict[str, Any], 
                            predicted_fct: float):
        """
        添加流完成事件
        
        Args:
            timestamp: 完成时间
            flow: 流数据
            predicted_fct: 预测的流完成时间
        """
        data = {
            "flow": flow,
            "flow_id": flow.get("flow_id", "unknown"),
            "predicted_fct": predicted_fct,
            "actual_duration": timestamp - flow.get("start_time", 0.0)
        }
        self.add_event(timestamp, EventType.FINISH_FLOW, data)
    
    def get_next_event(self) -> Optional[Event]:
        """
        获取下一个事件（时间戳最小的事件）
        
        Returns:
            下一个事件，如果队列为空则返回None
        """
        if self.is_empty():
            return None
        
        event = heapq.heappop(self.event_queue)
        self.current_time = event.timestamp
        self.processed_events += 1
        
        logger.debug(f"处理事件: {event.event_type.value} at {event.timestamp:.4f}")
        return event
    
    def peek_next_event(self) -> Optional[Event]:
        """
        查看下一个事件但不移除
        
        Returns:
            下一个事件，如果队列为空则返回None
        """
        if self.is_empty():
            return None
        return self.event_queue[0]
    
    def is_empty(self) -> bool:
        """
        检查事件队列是否为空
        
        Returns:
            队列是否为空
        """
        return len(self.event_queue) == 0
    
    def size(self) -> int:
        """
        获取队列中剩余事件数量
        
        Returns:
            剩余事件数量
        """
        return len(self.event_queue)
    
    def get_current_time(self) -> float:
        """
        获取当前模拟时间
        
        Returns:
            当前时间
        """
        return self.current_time
    
    def set_current_time(self, time: float):
        """
        设置当前模拟时间
        
        Args:
            time: 新的当前时间
        """
        self.current_time = time
    
    def advance_to_time(self, target_time: float) -> List[Event]:
        """
        推进到指定时间，返回期间的所有事件
        
        Args:
            target_time: 目标时间
            
        Returns:
            期间发生的事件列表
        """
        events = []
        
        while not self.is_empty():
            next_event = self.peek_next_event()
            if next_event.timestamp <= target_time:
                event = self.get_next_event()
                events.append(event)
            else:
                break
        
        # 更新当前时间到目标时间
        if events or self.current_time < target_time:
            self.current_time = target_time
        
        return events
    
    def clear_events_after(self, timestamp: float):
        """
        清除指定时间之后的所有事件
        
        Args:
            timestamp: 截止时间
        """
        # 重建队列，只保留时间戳<=timestamp的事件
        valid_events = []
        
        while not self.is_empty():
            event = heapq.heappop(self.event_queue)
            if event.timestamp <= timestamp:
                valid_events.append(event)
        
        # 重新构建堆
        self.event_queue = valid_events
        heapq.heapify(self.event_queue)
        
        logger.info(f"清除了时间 {timestamp} 之后的事件，保留 {len(valid_events)} 个事件")
    
    def get_events_by_type(self, event_type: EventType) -> List[Event]:
        """
        获取指定类型的所有事件（不移除）
        
        Args:
            event_type: 事件类型
            
        Returns:
            指定类型的事件列表
        """
        return [event for event in self.event_queue if event.event_type == event_type]
    
    def get_events_by_flow(self, flow_id: str) -> List[Event]:
        """
        获取指定流的所有事件（不移除）
        
        Args:
            flow_id: 流ID
            
        Returns:
            指定流的事件列表
        """
        return [event for event in self.event_queue 
                if event.data.get("flow_id") == flow_id]
    
    def remove_events_by_flow(self, flow_id: str) -> int:
        """
        移除指定流的所有事件
        
        Args:
            flow_id: 流ID
            
        Returns:
            移除的事件数量
        """
        original_size = len(self.event_queue)
        
        # 过滤掉指定流的事件
        self.event_queue = [event for event in self.event_queue 
                           if event.data.get("flow_id") != flow_id]
        
        # 重新构建堆
        heapq.heapify(self.event_queue)
        
        removed_count = original_size - len(self.event_queue)
        logger.info(f"移除了流 {flow_id} 的 {removed_count} 个事件")
        
        return removed_count
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取事件管理器统计信息
        
        Returns:
            统计信息字典
        """
        event_type_counts = {}
        for event in self.event_queue:
            event_type = event.event_type.value
            event_type_counts[event_type] = event_type_counts.get(event_type, 0) + 1
        
        return {
            "current_time": self.current_time,
            "pending_events": len(self.event_queue),
            "processed_events": self.processed_events,
            "total_events_added": self.total_events_added,
            "event_type_distribution": event_type_counts,
            "next_event_time": self.event_queue[0].timestamp if self.event_queue else None
        }
    
    def save_state(self) -> Dict[str, Any]:
        """
        保存事件管理器状态（用于跨调度间隔的状态保持）
        
        Returns:
            状态字典
        """
        # 将事件队列序列化
        serialized_events = []
        for event in self.event_queue:
            serialized_events.append({
                "timestamp": event.timestamp,
                "event_type": event.event_type.value,
                "data": event.data
            })
        
        return {
            "current_time": self.current_time,
            "processed_events": self.processed_events,
            "total_events_added": self.total_events_added,
            "events": serialized_events
        }
    
    def load_state(self, state: Dict[str, Any]):
        """
        从状态字典恢复事件管理器状态
        
        Args:
            state: 状态字典
        """
        self.current_time = state.get("current_time", 0.0)
        self.processed_events = state.get("processed_events", 0)
        self.total_events_added = state.get("total_events_added", 0)
        
        # 恢复事件队列
        self.event_queue = []
        for event_data in state.get("events", []):
            event_type = EventType(event_data["event_type"])
            event = Event(
                timestamp=event_data["timestamp"],
                event_type=event_type,
                data=event_data["data"]
            )
            heapq.heappush(self.event_queue, event)
        
        logger.info(f"恢复事件管理器状态: 当前时间={self.current_time}, "
                   f"待处理事件={len(self.event_queue)}")
    
    def reset(self):
        """重置事件管理器到初始状态"""
        self.event_queue.clear()
        self.current_time = 0.0
        self.processed_events = 0
        self.total_events_added = 0
        
        logger.info("事件管理器已重置")
    
    def validate_consistency(self) -> bool:
        """
        验证事件队列的一致性（调试用）
        
        Returns:
            是否一致
        """
        if not self.event_queue:
            return True
        
        # 检查堆性质
        for i in range(len(self.event_queue)):
            left_child = 2 * i + 1
            right_child = 2 * i + 2
            
            if (left_child < len(self.event_queue) and 
                self.event_queue[i].timestamp > self.event_queue[left_child].timestamp):
                return False
            
            if (right_child < len(self.event_queue) and 
                self.event_queue[i].timestamp > self.event_queue[right_child].timestamp):
                return False
        
        return True 
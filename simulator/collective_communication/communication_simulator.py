#!/usr/bin/env python3
"""
通信模拟器 - 事件驱动模拟器主类
协调所有模拟组件，执行事件驱动的Ring All-Reduce通信模拟
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
import copy

from .event_manager import EventManager, EventType
from .ring_all_reduce_tracker import RingAllReduceTracker, JobStatus
from .flow_generator import FlowGenerator
from .flow_manager import FlowManager
from .prediction_buffer import PredictionBuffer

logger = logging.getLogger(__name__)

class CommunicationSimulator:
    """事件驱动通信模拟器主类"""
    
    def __init__(self, flow_generator: FlowGenerator = None, 
                 topology_manager = None, prediction_service = None,
                 enable_routing_optimizer: bool = False):
        """
        初始化通信模拟器
        
        Args:
            flow_generator: 流生成器实例
            topology_manager: 拓扑管理器实例
            prediction_service: 预测服务实例
            enable_routing_optimizer: 是否启用动态路由优化
        """
        self.flow_generator = flow_generator
        self.topology_manager = topology_manager
        self.prediction_service = prediction_service
        self.enable_routing_optimizer = enable_routing_optimizer
        
        # 核心组件
        self.event_manager = EventManager()
        self.tracker = RingAllReduceTracker(flow_generator, topology_manager)
        
        # 全局流状态维护 - 使用FlowManager优化
        self.flow_manager = FlowManager()  # 高效的流管理器

        # 预测缓冲系统
        self.prediction_buffer = PredictionBuffer(buffer_size_limit=20, time_window=0.05, prediction_service=self.prediction_service)
        self.enable_batch_prediction = True

        # 向后兼容性：保留原接口
        self._legacy_mode = False  # 是否使用传统模式

        # 轮次批量删除机制
        self.completed_rounds_tracker = {}  # 每个作业的已完成轮次追踪 {job_name: set(round_idx)}
        self.batch_deletion_enabled = True  # 批量删除开关

        # 性能监控统计
        self.deletion_stats = {
            'total_deletions': 0,
            'total_deleted_flows': 0,
            'total_deletion_time': 0.0,
            'max_flows_before_deletion': 0,
            'min_flows_after_deletion': float('inf')
        }
        
        # 模拟状态
        self.current_interval_start = 0.0
        self.current_interval_duration = 60.0
        self.job_registry = {}  # {job_name: job_object}

        # 多次Ring All-Reduce控制
        self.enable_multiple_allreduces = True  # 是否启用多次通信
        self.completed_allreduces = {}  # {job_name: [completion_times]}
        self.allreduce_count = {}  # {job_name: count}
        self.current_placement = {}  # {job_name: placement}
        
        # 统计信息
        self.total_events_processed = 0
        self.total_flows_simulated = 0
        self.simulation_time_elapsed = 0.0
        
        logger.info("通信模拟器初始化完成")

    @property
    def all_known_flows(self) -> List[Dict]:
        """向后兼容：获取所有已知流的列表"""
        return self.flow_manager.get_all_flows()

    @property
    def flow_id_to_index(self) -> Dict[str, int]:
        """向后兼容：模拟流ID到索引的映射（已废弃，但保持兼容性）"""
        # 注意：这个属性已经不再使用索引，但为了兼容性保留
        flows = self.flow_manager.get_all_flows()
        return {flow['flow_id']: i for i, flow in enumerate(flows)}

    def run(self, final_routing_plan: Dict[str, List[str]],
            active_jobs: List, interval_duration: float = 60.0) -> Dict[str, float]:
        """
        运行事件驱动通信模拟，支持多次Ring All-Reduce

        Args:
            final_routing_plan: 路由优化结果 {flow_id: path}
            active_jobs: 活跃作业列表
            interval_duration: 调度间隔时长（秒）

        Returns:
            平均通信时间结果 {job_name: average_communication_time}
        """
        logger.info(f"开始事件驱动通信模拟，调度间隔={interval_duration}秒，多次Ring All-Reduce={'启用' if self.enable_multiple_allreduces else '禁用'}")

        # 初始化模拟状态
        self.current_interval_duration = interval_duration
        self._initialize_simulation(active_jobs, final_routing_plan)

        if self.enable_multiple_allreduces:
            # 执行多次Ring All-Reduce模拟
            communication_times = self._execute_multiple_allreduces(active_jobs, interval_duration)
        else:
            # 执行单次Ring All-Reduce模拟（原有逻辑）
            self._prepare_initial_events(active_jobs)
            raw_communication_times = self._execute_event_loop(interval_duration)
            communication_times = {
                job_name: time
                for job_name, time in raw_communication_times.items()
            }
        
        # 强制刷新预测缓冲区（确保所有待预测流都被处理）
        if self.enable_batch_prediction and self.prediction_buffer.get_pending_flow_count() > 0:
            logger.debug("通信模拟结束前强制刷新预测缓冲区")
            remaining_predictions = self.prediction_buffer.flush_and_predict(
                self.prediction_service, self.all_known_flows
            )
            
            # 处理剩余的预测结果
            for flow_id, predicted_fct in remaining_predictions.items():
                predicted_fct = 80 * predicted_fct / 1024  # 暂时处理
                self._update_flow_predicted_fct(flow_id, predicted_fct)
                
                # 为剩余流创建FINISH_FLOW事件
                flow = self.flow_manager.get_flow_by_id(flow_id)
                if flow:
                    start_time = flow.get('start_time', 0.0)
                    finish_time = start_time + predicted_fct
                    self.event_manager.add_finish_flow_event(finish_time, flow, predicted_fct)
                    logger.debug(f"流 {flow_id} 最终刷新FCT={predicted_fct:.8f}秒")

        # 清理和统计
        self._finalize_simulation()
        
        logger.debug(f"通信模拟完成，处理了 {self.total_events_processed} 个事件")
        return communication_times
    
    def _initialize_simulation(self, active_jobs: List, final_routing_plan: Dict[str, List[str]]):
        """
        初始化模拟状态（保护流数据不被清空）

        Args:
            active_jobs: 活跃作业列表
            final_routing_plan: 路由方案
        """
        # 记录初始化前的流数量用于验证
        flows_before_init = self.flow_manager.get_flow_count()
        logger.debug(f"初始化前流数量: {flows_before_init}")

        # 重置事件和追踪状态（但保留流数据）
        self.event_manager.reset()
        self.tracker.reset()
        self.job_registry.clear()

        # 关键修复：不清空FlowManager，保护已设置的流数据
        # self.flow_manager.clear()  # 注释掉这行，避免清空流数据

        # 重置批量删除相关状态
        self.completed_rounds_tracker.clear()
        self.deletion_stats = {
            'total_deletions': 0,
            'total_deleted_flows': 0,
            'total_deletion_time': 0.0,
            'max_flows_before_deletion': 0,
            'min_flows_after_deletion': float('inf')
        }

        # 验证流数据完整性
        flows_after_init = self.flow_manager.get_flow_count()
        if flows_after_init != flows_before_init:
            logger.warning(f"初始化后流数量变化: {flows_before_init} -> {flows_after_init}")
        else:
            logger.debug(f"初始化后流数据完整性验证通过: {flows_after_init} 个流")
        
        # 注册作业并构建placement
        self.current_placement = {}
        for job in active_jobs:
            self.job_registry[job.name] = job

            # 构建placement字典
            if hasattr(job, 'placement') and job.placement:
                node_allocation = []
                current_node = 0
                for gpu_count in job.placement:
                    for _ in range(gpu_count):
                        node_allocation.append(current_node)
                    current_node += 1
                self.current_placement[job.name] = node_allocation
        
        # 应用路由方案到流
        self._apply_routing_plan(final_routing_plan)
        
        logger.info(f"初始化完成: {len(active_jobs)} 个作业, {len(self.all_known_flows)} 个流")
    
    def _apply_routing_plan(self, routing_plan: Dict[str, List[str]]):
        """
        应用路由方案到所有已知流
        
        Args:
            routing_plan: 路由方案
        """
        for flow in self.all_known_flows:
            flow_id = flow.get("flow_id")
            if flow_id in routing_plan:
                flow["path"] = routing_plan[flow_id]
                logger.debug(f"应用路由: {flow_id} -> {routing_plan[flow_id]}")
    
    def _prepare_initial_events(self, active_jobs: List):
        """
        准备初始事件（第一轮通信流的START_FLOW事件）- 增强错误处理版本

        Args:
            active_jobs: 活跃作业列表
        """
        total_events_created = 0
        successful_jobs = 0

        logger.info(f"开始为 {len(active_jobs)} 个作业准备初始事件")

        for job in active_jobs:
            try:
                # 计算作业的计算时间
                computation_time = self._calculate_computation_time(job)

                # 第一轮通信的开始时间 = 间隔开始时间 + 计算时间
                first_round_start_time = self.current_interval_start + computation_time

                # 获取作业的第一轮流
                first_round_flows = self._get_job_first_round_flows(job)

                if first_round_flows:
                    # 计算Ring大小
                    ring_size = self._calculate_ring_size(job)

                    # 初始化作业追踪
                    self.tracker.initialize_job(job, ring_size, first_round_flows)

                    # 关键修复：启动作业通信，记录第一轮开始时间
                    self.tracker.start_job_communication(job.name, first_round_start_time)

                    # 添加第一轮流的START_FLOW事件
                    for flow in first_round_flows:
                        flow["start_time"] = first_round_start_time
                        self.event_manager.add_start_flow_event(first_round_start_time, flow)
                        total_events_created += 1

                    successful_jobs += 1
                    logger.info(f"✅ 作业 {job.name}: 创建了 {len(first_round_flows)} 个初始事件，开始时间 {first_round_start_time:.4f}")
                else:
                    logger.error(f"❌ 作业 {job.name}: 无法找到第一轮流，跳过初始事件创建")

            except Exception as e:
                logger.error(f"❌ 作业 {job.name}: 准备初始事件时出错: {e}")

        # 总结初始事件准备结果
        logger.info(f"初始事件准备完成: {successful_jobs}/{len(active_jobs)} 个作业成功，总共创建 {total_events_created} 个事件")

        # 验证事件队列状态
        event_count = self.event_manager.size()
        if event_count == 0:
            logger.error("⚠️  严重问题：事件队列为空！无法启动模拟。")
        else:
            logger.info(f"事件队列状态: {event_count} 个待处理事件")
    
    def _calculate_computation_time(self, job) -> float:
        """
        计算作业的计算时间
        
        Args:
            job: 作业对象
            
        Returns:
            计算时间（秒）
        """
        try:
            # 获取当前配置
            placement = tuple(filter(None, job.placement)) if hasattr(job, 'placement') else (1,)
            atomic_bsz = getattr(job, 'atomic_bsz', 32)
            
            # 使用应用的吞吐量模型计算
            step_time, sync_time = job.application.get_throughput(placement, atomic_bsz)
            compute_time = step_time - sync_time  # 纯计算时间
            
            return compute_time # 最少0.1秒
            
        except Exception as e:
            logger.warning(f"计算作业 {job.name} 计算时间时出错，使用默认值: {e}")
            return 1.0  # 默认1秒
    
    def _get_job_first_round_flows(self, job) -> List[Dict]:
        """
        获取作业第一轮的流（增强健壮性版本）

        Args:
            job: 作业对象

        Returns:
            第一轮流列表
        """
        # 首先检查流数据是否存在
        total_flows = self.flow_manager.get_flow_count()
        if total_flows == 0:
            logger.error(f"无法获取作业 {job.name} 的第一轮流：流数据为空！这可能是初始化顺序问题。")
            return []

        logger.debug(f"从 {total_flows} 个流中查找作业 {job.name} 的第一轮流")

        # 从all_known_flows中筛选出该作业第一轮的流
        job_first_round_flows = []

        for flow in self.all_known_flows:
            if (flow.get("job_name") == job.name and
                flow.get("round_idx") == 0):
                job_first_round_flows.append(flow)

        # 详细的结果日志
        if job_first_round_flows:
            logger.debug(f"成功找到作业 {job.name} 的第一轮流: {len(job_first_round_flows)} 个")
        else:
            logger.warning(f"未找到作业 {job.name} 的第一轮流！检查作业名称和轮次索引是否正确。")
            # 提供诊断信息
            job_flows = [f for f in self.all_known_flows if f.get("job_name") == job.name]
            if job_flows:
                rounds = set(f.get("round_idx") for f in job_flows)
                logger.warning(f"作业 {job.name} 存在的轮次: {sorted(rounds)}")
            else:
                logger.warning(f"作业 {job.name} 在流数据中不存在")

        return job_first_round_flows
    
    def _calculate_ring_size(self, job) -> int:
        """
        计算作业的Ring大小 (即其占用的节点数量)
        
        Args:
            job: 作业对象，其 job.placement 属性是一个元组，
                 例如 (4, 4, 3)，代表作业使用了3个节点，
                 分别占用了4、4、3个GPU。
                 
        Returns:
            作业占用的节点数，作为Ring的大小。
        """
        try:
            # job.placement 是一个元组，其长度代表作业跨越的节点数。
            if hasattr(job, 'placement') and job.placement:
                # 正确的逻辑是直接获取 placement 元组的长度。
                return len(job.placement)
            else:
                # 如果没有分配 placement，则认为它不参与跨节点通信，Ring大小为1（或0，但1更安全）。
                return 1
        except Exception as e:
            # 增加日志记录，以便于调试未来可能出现的未知错误。
            logger.error(f"计算作业 {job.name} 的 Ring 大小时出现未知错误: {e}")
            return 1
    
    def _execute_event_loop(self, interval_duration: float) -> Dict[str, float]:
        """
        执行主事件循环
        
        Args:
            interval_duration: 间隔时长
            
        Returns:
            通信时间结果
        """
        interval_end_time = self.current_interval_start + interval_duration
        logger.info(f"=============开始事件循环=============：间隔时间 [{self.current_interval_start:.4f}, {interval_end_time:.4f}]")
        
        loop_iteration = 0
        while not self.event_manager.is_empty():
            loop_iteration += 1
            
            # 获取下一个事件
            next_event = self.event_manager.peek_next_event()
            
            # 检查是否超出调度间隔
            if next_event.timestamp > interval_end_time:
                logger.info(f"事件时间 {next_event.timestamp:.4f} 超出间隔结束时间 {interval_end_time:.4f}，保存状态并停止模拟")
                # 保存未完成的事件状态，而不是直接丢弃
                self._save_cross_interval_state(interval_end_time)
                break
            
            # 处理事件
            event = self.event_manager.get_next_event()
            logger.debug(f"循环 {loop_iteration}: 处理事件 {event.event_type.value} @ {event.timestamp:.4f}")
            
            self._process_event(event)
            self.total_events_processed += 1
            
            # 定期报告进度
            if loop_iteration % 10 == 0:
                remaining_events = self.event_manager.size()
                logger.debug(f"事件循环进度: 已处理 {loop_iteration} 个事件，队列剩余 {remaining_events} 个事件")
        
        logger.info(f"事件循环结束：总共处理了 {loop_iteration} 个事件")
        
        # 收集通信时间结果
        return self._collect_communication_times()
    
    def _process_event(self, event):
        """
        处理单个事件
        
        Args:
            event: 事件对象
        """
        if event.event_type == EventType.START_FLOW:
            self._handle_start_flow_event(event)
        elif event.event_type == EventType.FINISH_FLOW:
            self._handle_finish_flow_event(event)
        elif event.event_type == EventType.RETRY_NEW_CYCLE:
            self._handle_retry_new_cycle_event(event)
        else:
            logger.warning(f"未知事件类型: {event.event_type}")
    
    def _handle_start_flow_event(self, event):
        """
        处理流开始事件
        
        Args:
            event: START_FLOW事件
        """
        flow = event.data["flow"]
        flow_id = flow["flow_id"]
        start_time = event.timestamp
        
        # 更新流状态
        self._update_flow_status(flow, "ACTIVE", start_time)
        
        # 通知追踪器
        self.tracker.start_flow(flow_id, flow, start_time)
        
        if self.enable_batch_prediction:
            # 使用缓冲机制进行批量预测
            self._buffer_flow_prediction(flow)
            # 检查是否需要刷新缓冲区
            self._flush_prediction_buffer_if_needed(start_time)
        else:
            # 原有的单流预测逻辑
            predicted_fct = self._predict_flow_completion_time(flow)
            predicted_fct = 80 * predicted_fct / 1024 # 暂时处理
            self._update_flow_predicted_fct(flow_id, predicted_fct)
            
            # 添加FINISH_FLOW事件
            finish_time = start_time + predicted_fct
            self.event_manager.add_finish_flow_event(finish_time, flow, predicted_fct)
            
            logger.debug(f"流 {flow_id} 开始，预测FCT={predicted_fct:.8f}秒，完成时间={finish_time:.8f}")
        
        self.total_flows_simulated += 1
    
    def _buffer_flow_prediction(self, flow: Dict) -> None:
        """
        将流添加到预测缓冲区
        
        Args:
            flow: 流数据
        """
        flow_id = flow.get('flow_id', 'unknown')
        start_time = flow.get('start_time', 0.0)
        
        # 添加到预测缓冲区
        self.prediction_buffer.add_flow_for_prediction(flow, start_time)
        logger.debug(f"流 {flow_id} 已添加到预测缓冲区，待预测流数量: {self.prediction_buffer.get_pending_flow_count()}")
    
    def _flush_prediction_buffer_if_needed(self, current_time: float) -> None:
        """
        检查并刷新预测缓冲区
        
        Args:
            current_time: 当前时间
        """
        if self.prediction_buffer.should_flush_buffer(current_time):
            logger.debug("触发预测缓冲区刷新")
            
            # 执行批量预测
            prediction_results = self.prediction_buffer.flush_and_predict(
                self.prediction_service, self.all_known_flows
            )
            
            # 处理预测结果
            for flow_id, predicted_fct in prediction_results.items():
                predicted_fct = 80 * predicted_fct / 1024  # 暂时处理
                self._update_flow_predicted_fct(flow_id, predicted_fct)
                
                # 为每个流创建FINISH_FLOW事件
                flow = self.flow_manager.get_flow_by_id(flow_id)
                if flow:
                    start_time = flow.get('start_time', current_time)
                    finish_time = start_time + predicted_fct
                    self.event_manager.add_finish_flow_event(finish_time, flow, predicted_fct)
                    logger.debug(f"流 {flow_id} 批量预测FCT={predicted_fct:.8f}秒，完成时间={finish_time:.8f}")
    
    def _handle_finish_flow_event(self, event):
        """
        处理流完成事件（修复版本，解决状态更新时序问题）
        
        Args:
            event: FINISH_FLOW事件
        """
        flow = event.data["flow"]
        flow_id = flow["flow_id"]
        end_time = event.timestamp
        
        # 🔧 修复：先通知追踪器，然后根据结果决定状态更新
        next_round_flows = self.tracker.finish_flow(flow_id, end_time)
        
        # 无论Tracker是否成功，都要更新FlowManager状态以保持一致性
        # 但记录Tracker处理结果以便调试
        if next_round_flows is None and flow_id not in self.tracker.flow_to_job:
            logger.warning(f"⚠️  流 {flow_id} 在Tracker中找不到映射，但仍更新FlowManager状态")
        
        # 更新FlowManager状态
        self._update_flow_status(flow, "COMPLETED", end_time)
        
        # 关键修复：如果tracker没有返回流，主动生成下一轮流
        if not next_round_flows:
            # 获取该流对应的作业
            job_name = None
            for flow_key, job in self.tracker.flow_to_job.items():
                if flow_key == flow_id:
                    job_name = job
                    break
            
            if job_name and job_name in self.job_registry:
                job = self.job_registry[job_name]
                current_round = self.tracker.flow_to_round.get(flow_id, 0)
                
                # 检查该轮次是否完成，如果是则生成下一轮
                if self.tracker._is_round_completed(job_name, current_round):
                    next_round_flows = self.tracker.generate_next_round_flows(job, current_round)
                    logger.debug(f"主动生成作业 {job_name} 下一轮流: {len(next_round_flows)} 个流")
        
        # 如果有新轮次流，生成并添加它们的START_FLOW事件
        if next_round_flows:
            self._schedule_next_round_flows_with_dynamic_routing(next_round_flows, end_time)

        # 集成轮次完成检测和批量删除触发
        if flow_id in self.tracker.flow_to_job:
            job_name = self.tracker.flow_to_job[flow_id]
            current_round = self.tracker.flow_to_round.get(flow_id, 0)

            # 检查该轮次是否完成
            if self.tracker._is_round_completed(job_name, current_round):
                self._track_round_completion(job_name, current_round)

        logger.debug(f"流 {flow_id} 完成，时间={end_time:.4f}")
    
    def _predict_flow_completion_time(self, flow: Dict) -> float:
        """
        使用预测服务预测流完成时间
        
        Args:
            flow: 流数据
            
        Returns:
            预测的完成时间（秒）
        """
        try:
            # 关键修复：归一化时间特征避免域偏移
            flow_for_prediction = flow.copy()
            
            # 将绝对开始时间转换为相对间隔时间（0-60秒范围）
            absolute_start_time = flow_for_prediction.get("start_time", 0.0)
            normalized_start_time = self._normalize_time_for_prediction(
                absolute_start_time, self.current_interval_start, self.current_interval_duration
            )
            flow_for_prediction["start_time"] = normalized_start_time
            
            logger.debug(f"流 {flow.get('flow_id')} 时间归一化: {absolute_start_time:.4f} -> {normalized_start_time:.4f}")
            
            # 准备预测输入
            flows_to_predict = [flow_for_prediction]
            
            # 调用预测服务
            predicted_fcts = self.prediction_service.predict_fct(flows_to_predict, self.all_known_flows)
            
            if predicted_fcts:
                return max(predicted_fcts[0], 0.001)  # 最少1毫秒
            else:
                return 1.0  # 默认1秒
                
        except Exception as e:
            logger.warning(f"预测流 {flow.get('flow_id')} FCT时出错，使用默认值: {e}")
            return 1.0
    
    def _normalize_time_for_prediction(self, absolute_time: float, interval_start: float, interval_duration: float) -> float:
        """
        将绝对时间转换为相对间隔时间，避免预测模型域偏移
        
        Args:
            absolute_time: 绝对模拟时间
            interval_start: 当前间隔开始时间
            interval_duration: 间隔持续时间
            
        Returns:
            归一化的相对时间（0-60秒范围内）
        """
        # 计算相对于当前间隔的时间偏移
        relative_time = absolute_time - interval_start
        
        # 归一化到0-60秒范围（模型训练数据的时间范围）
        normalized_time = (relative_time % interval_duration) / interval_duration * 60.0
        
        # 确保在有效范围内
        normalized_time = min(max(normalized_time, 0.0), 60.0)
        
        return normalized_time
    
    def _schedule_next_round_flows_with_dynamic_routing(self, next_round_flows: List[Dict], start_time: float):
        """
        调度下一轮流的START_FLOW事件，并进行动态路由优化
        
        Args:
            next_round_flows: 下一轮流列表
            start_time: 开始时间
        """
        if not next_round_flows:
            return
        
        # 为新流分配候选路径
        for flow in next_round_flows:
            self._assign_candidate_paths(flow)
        
        # 根据标志决定是否执行动态路由优化
        if self.enable_routing_optimizer:
            logger.debug("执行动态路由优化...")
            optimized_routing = self._perform_dynamic_routing_optimization(next_round_flows, start_time)
        else:
            logger.debug("跳过路由优化，使用默认ECMP路径")
            optimized_routing = {}
        
        # 应用优化后的路由并调度流 - 增强流同步版本
        successfully_added = 0
        for flow in next_round_flows:
            flow_id = flow["flow_id"]

            # 关键修复：确保流正确添加到FlowManager
            logger.debug(f"🔍 尝试添加动态流 {flow_id}，状态: {flow.get('status')}, 轮次: {flow.get('round_idx')}")
            if self._add_flow_to_known_flows_with_verification(flow):
                # 应用优化后的路由
                if flow_id in optimized_routing:
                    flow["path"] = optimized_routing[flow_id]
                    logger.debug(f"流 {flow_id} 应用优化路由: {optimized_routing[flow_id]}")

                # 设置开始时间
                flow["start_time"] = start_time

                # 添加START_FLOW事件
                self.event_manager.add_start_flow_event(start_time, flow)
                successfully_added += 1
                logger.debug(f"✅ 动态流 {flow_id} 成功调度，START_FLOW事件已添加")
            else:
                logger.error(f"❌ 动态流 {flow_id} 添加到FlowManager失败，跳过调度")

        logger.info(f"动态流调度完成: {successfully_added}/{len(next_round_flows)} 个流成功添加并调度")
        
        if self.enable_routing_optimizer:
            logger.debug(f"调度了 {len(next_round_flows)} 个下一轮流，开始时间={start_time:.4f}，应用动态路由优化")
        else:
            logger.debug(f"调度了 {len(next_round_flows)} 个下一轮流，开始时间={start_time:.4f}，使用默认ECMP路径")
    

    
    def _update_flow_status(self, flow: Dict, status: str, timestamp: float):
        """
        更新流状态

        Args:
            flow: 流数据
            status: 新状态
            timestamp: 时间戳
        """
        flow_id = flow.get("flow_id")

        # 更新流对象
        flow["status"] = status
        if status == "ACTIVE":
            flow["start_time"] = timestamp
        elif status == "COMPLETED":
            flow["end_time"] = timestamp
            if "start_time" in flow:
                flow["actual_duration"] = timestamp - flow["start_time"]

        # 更新FlowManager中的状态分类（如果流仍然存在）
        if flow_id:
            if self.flow_manager.get_flow_by_id(flow_id):
                self.flow_manager.update_flow_status(flow_id, status)
            else:
                logger.debug(f"流 {flow_id} 在FlowManager中不存在，跳过状态更新（可能已被删除）")
    
    def _add_flow_to_known_flows(self, flow: Dict):
        """
        将新流添加到已知流列表

        Args:
            flow: 流数据
        """
        self.flow_manager.add_flow(flow)

    def _add_flow_to_known_flows_with_verification(self, flow: Dict) -> bool:
        """
        将新流添加到已知流列表并验证成功

        Args:
            flow: 流数据

        Returns:
            是否添加成功
        """
        flow_id = flow.get("flow_id")
        if not flow_id:
            logger.error("流缺少flow_id，无法添加")
            return False

        # 检查是否已存在
        existing_flow = self.flow_manager.get_flow_by_id(flow_id)
        if existing_flow:
            logger.debug(f"流 {flow_id} 已存在，更新现有流")
            # 更新现有流的属性
            for key, value in flow.items():
                existing_flow[key] = value
            return True

        # 添加新流
        success = self.flow_manager.add_flow(flow)
        if success:
            # 验证添加成功
            verification_flow = self.flow_manager.get_flow_by_id(flow_id)
            if verification_flow:
                logger.debug(f"✅ 动态流 {flow_id} 成功添加到FlowManager")
                return True
            else:
                logger.error(f"❌ 动态流 {flow_id} 添加验证失败")
                return False
        else:
            logger.error(f"❌ 动态流 {flow_id} 添加到FlowManager失败")
            return False

    def _update_flow_predicted_fct(self, flow_id: str, predicted_fct: float):
        """
        更新流的预测完成时间 - 增强错误处理版本

        Args:
            flow_id: 流ID
            predicted_fct: 预测的完成时间
        """
        # 首先检查流是否存在
        flow = self.flow_manager.get_flow_by_id(flow_id)
        if not flow:
            logger.debug(f"流 {flow_id} 在FlowManager中不存在，跳过FCT更新（可能已被删除）")
            return

        # 尝试更新流的FCT
        if self.flow_manager.update_flow_field(flow_id, "predicted_fct", predicted_fct):
            logger.debug(f"更新流 {flow_id} 的预测FCT: {predicted_fct:.4f}秒")
            return

        # 更新失败，尝试恢复
        logger.debug(f"流 {flow_id} FCT更新失败，尝试恢复...")

        # 检查流是否存在于FlowManager中
        flow = self.flow_manager.get_flow_by_id(flow_id)
        if flow:
            # 流存在但更新失败，直接修改流对象
            flow["predicted_fct"] = predicted_fct
            logger.debug(f"通过直接修改恢复流 {flow_id} 的FCT更新")
        else:
            # 流不存在，进行智能恢复判断
            logger.debug(f"流 {flow_id} 在FlowManager中不存在，进行智能恢复判断")

            # 智能判断是否应该恢复流
            if self._should_attempt_flow_recovery(flow_id):
                recovered_flow = self._attempt_flow_recovery(flow_id, predicted_fct)
                if recovered_flow:
                    logger.info(f"✅ 成功恢复流 {flow_id} 并更新FCT")
                else:
                    logger.debug(f"流 {flow_id} 恢复失败，可能已被正确删除")
            else:
                logger.debug(f"流 {flow_id} 不需要恢复，可能已被正确删除或不应存在")

    def _attempt_flow_recovery(self, flow_id: str, predicted_fct: float) -> bool:
        """
        尝试恢复丢失的流

        Args:
            flow_id: 流ID
            predicted_fct: 预测FCT

        Returns:
            是否恢复成功
        """
        try:
            # 从流ID解析基本信息
            parts = flow_id.split('_')
            if len(parts) >= 4:
                job_name = '_'.join(parts[:-3])  # 作业名称可能包含下划线
                round_part = parts[-3]  # roundX
                src_part = parts[-2]    # srcHX
                dst_part = parts[-1]    # dstHX

                if round_part.startswith('round') and src_part.startswith('src') and dst_part.startswith('dst'):
                    round_idx = int(round_part[5:])
                    src_host = src_part[3:]
                    dst_host = dst_part[3:]

                    # 创建恢复的流对象
                    recovered_flow = {
                        "flow_id": flow_id,
                        "job_name": job_name,
                        "round_idx": round_idx,
                        "src_host": src_host,
                        "dst_host": dst_host,
                        "status": "ACTIVE",
                        "predicted_fct": predicted_fct,
                        "flow_features": [10.0],  # 默认大小
                        "path": [],
                        "start_time": 0.0,
                        "model": "unknown",
                        "dataset": "unknown",
                        "parameters": 1000000.0
                    }

                    # 尝试添加到FlowManager
                    if self.flow_manager.add_flow(recovered_flow):
                        logger.debug(f"成功恢复并添加流 {flow_id}")
                        return True

            return False

        except Exception as e:
            logger.debug(f"流恢复失败: {e}")
            return False

    def _should_attempt_flow_recovery(self, flow_id: str) -> bool:
        """
        智能判断是否应该尝试恢复流

        Args:
            flow_id: 流ID

        Returns:
            是否应该尝试恢复
        """
        try:
            # 解析流ID获取基本信息
            parts = flow_id.split('_')
            if len(parts) < 4:
                logger.debug(f"流ID {flow_id} 格式不正确，不尝试恢复")
                return False

            job_name = '_'.join(parts[:-3])
            round_part = parts[-3]

            if not round_part.startswith('round'):
                logger.debug(f"流ID {flow_id} 轮次格式不正确，不尝试恢复")
                return False

            round_idx = int(round_part[5:])

            # 检查作业是否还活跃
            if job_name not in self.job_registry:
                logger.debug(f"作业 {job_name} 不在活跃作业列表中，不恢复流 {flow_id}")
                return False

            # 检查是否在已完成轮次中（已被批量删除的轮次不应恢复）
            if job_name in self.completed_rounds_tracker:
                if round_idx in self.completed_rounds_tracker[job_name]:
                    logger.debug(f"流 {flow_id} 属于已完成轮次 {round_idx}，不应恢复")
                    return False

            # 检查恢复频率（避免频繁恢复同一个流）
            if not hasattr(self, '_recovery_attempts'):
                self._recovery_attempts = {}

            recovery_count = self._recovery_attempts.get(flow_id, 0)
            if recovery_count >= 1:  # 最多尝试1次恢复
                logger.debug(f"流 {flow_id} 已尝试恢复 {recovery_count} 次，不再尝试")
                return False

            # 在判断时就记录尝试，防止重复判断
            self._recovery_attempts[flow_id] = recovery_count + 1

            logger.debug(f"流 {flow_id} 通过智能恢复判断，可以尝试恢复（第 {recovery_count + 1} 次）")
            return True

        except Exception as e:
            logger.debug(f"智能恢复判断出错: {e}")
            return False
    
    def _collect_communication_times(self) -> Dict[str, float]:
        """
        收集所有作业的通信时间
        
        Returns:
            通信时间字典 {job_name: total_communication_time}
        """
        communication_times = {}
        
        for job_name in self.job_registry:
            comm_time = self.tracker.get_job_communication_time(job_name)
            communication_times[job_name] = comm_time
            
            # 获取作业详细进度信息
            progress = self.tracker.get_job_progress(job_name)
            current_round = self.tracker.get_current_round(job_name)
            total_rounds = self.tracker.job_total_rounds.get(job_name, 0)
            job_status = self.tracker.get_job_status(job_name)
            
            logger.info(f"作业 {job_name}: 通信时间={comm_time:.4f}秒, " +
                       f"轮次进度={current_round + 1}/{total_rounds}, " +
                       f"状态={job_status.value}, " +
                       f"完成度={progress['progress']:.2%}")
        
        return communication_times
    
    def _finalize_simulation(self):
        """最终化模拟，清理资源和记录统计"""
        self.simulation_time_elapsed = self.event_manager.get_current_time() - self.current_interval_start
        
        # 记录统计信息
        stats = self.get_simulation_statistics()
        logger.debug(f"模拟统计: {stats}")
    
    def set_all_known_flows(self, flows: List[Dict]):
        """
        设置所有已知流（从流生成器获得）

        Args:
            flows: 所有流的列表
        """
        # 清空现有流
        self.flow_manager.clear()

        # 批量添加新流
        added_count = self.flow_manager.add_flows_batch(flows)

        logger.info(f"设置了 {added_count} 个已知流")
    
    def add_flows_to_known_flows(self, new_flows: List[Dict]):
        """
        向已知流列表添加新流

        Args:
            new_flows: 新流列表
        """
        added_count = self.flow_manager.add_flows_batch(new_flows)
        logger.debug(f"添加了 {added_count} 个新流到已知流列表")

    def _track_round_completion(self, job_name: str, round_idx: int):
        """
        追踪作业轮次完成情况

        Args:
            job_name: 作业名称
            round_idx: 完成的轮次索引
        """
        if job_name not in self.completed_rounds_tracker:
            self.completed_rounds_tracker[job_name] = set()

        self.completed_rounds_tracker[job_name].add(round_idx)
        logger.debug(f"追踪作业 {job_name} 轮次 {round_idx} 完成")

        # 触发批量删除：删除上一轮的已完成流
        if self.batch_deletion_enabled and round_idx > 0:
            target_round = round_idx - 1
            logger.debug(f"🗑️  准备批量删除作业 {job_name} 轮次 {target_round} 的已完成流")
            removed_count = self._batch_remove_completed_flows(job_name, target_round)
            if removed_count > 0:
                logger.info(f"批量删除作业 {job_name} 轮次 {target_round} 的 {removed_count} 个已完成流")
        else:
            logger.debug(f"🚫 不触发批量删除：batch_deletion_enabled={self.batch_deletion_enabled}, round_idx={round_idx}")

    def _batch_remove_completed_flows(self, job_name: str, target_round: int) -> int:
        """
        批量删除指定作业和轮次的已完成流（使用FlowManager优化版本）

        Args:
            job_name: 作业名称
            target_round: 目标轮次索引

        Returns:
            删除的流数量
        """
        import time
        start_time = time.time()
        flows_before = self.flow_manager.get_flow_count()

        try:
            # 使用FlowManager的高效批量删除
            removed_count = self.flow_manager.batch_remove_flows(job_name, target_round, 'COMPLETED')

            if removed_count > 0:
                # 更新性能统计
                end_time = time.time()
                deletion_time = end_time - start_time
                flows_after = self.flow_manager.get_flow_count()

                self.deletion_stats['total_deletions'] += 1
                self.deletion_stats['total_deleted_flows'] += removed_count
                self.deletion_stats['total_deletion_time'] += deletion_time
                self.deletion_stats['max_flows_before_deletion'] = max(
                    self.deletion_stats['max_flows_before_deletion'], flows_before)
                self.deletion_stats['min_flows_after_deletion'] = min(
                    self.deletion_stats['min_flows_after_deletion'], flows_after)

                logger.debug(f"批量删除完成：删除了 {removed_count} 个流，剩余 {flows_after} 个流，耗时 {deletion_time*1000:.2f}ms")

            return removed_count

        except Exception as e:
            logger.error(f"批量删除流时出错: {e}")
            return 0

    def _efficient_rebuild_flow_index(self):
        """
        高效重建流索引映射
        只对剩余流重建索引，而非全量重建
        """
        try:
            # 清空现有索引
            self.flow_id_to_index.clear()

            # 重建索引
            for i, flow in enumerate(self.all_known_flows):
                flow_id = flow.get("flow_id")
                if flow_id:
                    self.flow_id_to_index[flow_id] = i

            # 索引一致性验证
            expected_count = len(self.all_known_flows)
            actual_count = len(self.flow_id_to_index)

            if expected_count != actual_count:
                logger.warning(f"索引重建后数量不一致: 期望 {expected_count}, 实际 {actual_count}")
            else:
                logger.debug(f"索引重建完成: {actual_count} 个流")

        except Exception as e:
            logger.error(f"索引重建时出错: {e}")
            # 尝试恢复索引
            self._recover_flow_index()

    def _recover_flow_index(self):
        """
        索引恢复机制：在索引重建失败时尝试恢复
        """
        try:
            self.flow_id_to_index.clear()
            for i, flow in enumerate(self.all_known_flows):
                flow_id = flow.get("flow_id")
                if flow_id and flow_id not in self.flow_id_to_index:
                    self.flow_id_to_index[flow_id] = i
            logger.info(f"索引恢复完成: {len(self.flow_id_to_index)} 个流")
        except Exception as e:
            logger.error(f"索引恢复失败: {e}")

    def get_simulation_statistics(self) -> Dict[str, Any]:
        """
        获取模拟统计信息
        
        Returns:
            统计信息字典
        """
        event_stats = self.event_manager.get_statistics()
        tracker_stats = self.tracker.get_statistics()
        
        flow_manager_stats = self.flow_manager.get_statistics()

        return {
            "total_events_processed": self.total_events_processed,
            "total_flows_simulated": self.total_flows_simulated,
            "simulation_time_elapsed": self.simulation_time_elapsed,
            "interval_duration": self.current_interval_duration,
            "total_known_flows": self.flow_manager.get_flow_count(),
            "active_jobs": len(self.job_registry),
            "event_manager_stats": event_stats,
            "tracker_stats": tracker_stats,
            "batch_deletion_stats": self.deletion_stats.copy(),
            "flow_manager_stats": flow_manager_stats
        }
    
    def save_simulation_state(self) -> Dict[str, Any]:
        """
        保存模拟状态（用于跨调度间隔持久化）
        
        Returns:
            状态字典
        """
        return {
            "current_interval_start": self.current_interval_start,
            "current_interval_duration": self.current_interval_duration,
            "all_known_flows": self.all_known_flows,
            "flow_id_to_index": self.flow_id_to_index,
            "total_events_processed": self.total_events_processed,
            "total_flows_simulated": self.total_flows_simulated,
            "event_manager_state": self.event_manager.save_state(),
            "tracker_state": self.tracker.save_state(),
            # 保存多次Ring All-Reduce的实际状态（不保存预估次数）
            "completed_allreduces": self.completed_allreduces,
            "allreduce_count": self.allreduce_count
        }
    
    def load_simulation_state(self, state: Dict[str, Any]):
        """
        从状态字典恢复模拟状态
        
        Args:
            state: 状态字典
        """
        self.current_interval_start = state.get("current_interval_start", 0.0)
        self.current_interval_duration = state.get("current_interval_duration", 60.0)
        self.all_known_flows = state.get("all_known_flows", [])
        self.flow_id_to_index = state.get("flow_id_to_index", {})
        self.total_events_processed = state.get("total_events_processed", 0)
        self.total_flows_simulated = state.get("total_flows_simulated", 0)

        # 恢复多次Ring All-Reduce的实际状态（不恢复预估次数）
        self.completed_allreduces = state.get("completed_allreduces", {})
        self.allreduce_count = state.get("allreduce_count", {})
        
        # 恢复子组件状态
        if "event_manager_state" in state:
            self.event_manager.load_state(state["event_manager_state"])
        
        if "tracker_state" in state:
            self.tracker.load_state(state["tracker_state"])
        
        logger.info("通信模拟器状态恢复完成")
    
    def _assign_candidate_paths(self, flow: Dict):
        """
        为流分配候选路径
        
        Args:
            flow: 流数据
        """
        src_host = flow.get("src_host")
        dst_host = flow.get("dst_host")
        
        if src_host and dst_host and self.topology_manager:
            try:
                candidate_paths = self.topology_manager.get_ecmp_paths(src_host, dst_host)
                flow["candidate_paths"] = candidate_paths
                
                # 设置默认路径
                if candidate_paths:
                    flow["path"] = candidate_paths[0]
                    logger.debug(f"流 {flow.get('flow_id')} 分配候选路径: {len(candidate_paths)} 条")
                else:
                    logger.warning(f"流 {flow.get('flow_id')} 未找到候选路径")
                    flow["candidate_paths"] = []
                    flow["path"] = []
            except Exception as e:
                logger.error(f"为流 {flow.get('flow_id')} 分配路径时出错: {e}")
                flow["candidate_paths"] = []
                flow["path"] = []
    
    def _perform_dynamic_routing_optimization(self, new_flows: List[Dict], start_time: float) -> Dict[str, List[str]]:
        """
        执行动态路由优化
        
        Args:
            new_flows: 新生成的流列表
            start_time: 流开始时间
            
        Returns:
            优化后的路由方案 {flow_id: path}
        """
        if not new_flows:
            return {}
        
        try:
            # 检测路径冲突
            conflict_flows = self._detect_path_conflicts(new_flows, start_time)
            
            if not conflict_flows:
                logger.debug("未检测到路径冲突，使用默认路由")
                return {}
            
            logger.info(f"检测到 {len(conflict_flows)} 个流存在路径冲突，触发动态路由优化")
            
            # 调用路由优化器进行增量优化
            if hasattr(self, 'routing_optimizer') and self.routing_optimizer:
                # 构建优化输入：冲突流 + 相关背景流
                optimization_flows = self._prepare_optimization_flows(conflict_flows, start_time)
                
                # 执行优化
                self.routing_optimizer.set_current_time(start_time)
                optimized_routing = self.routing_optimizer.solve(optimization_flows)
                
                logger.info(f"动态路由优化完成，优化了 {len(optimized_routing)} 个流的路由")
                return optimized_routing
            else:
                logger.warning("路由优化器不可用，使用默认路由")
                return {}
                
        except Exception as e:
            logger.error(f"动态路由优化失败: {e}")
            return {}
    
    def _detect_path_conflicts(self, new_flows: List[Dict], start_time: float) -> List[Dict]:
        """
        检测新流与现有流的路径冲突
        
        Args:
            new_flows: 新流列表
            start_time: 开始时间
            
        Returns:
            存在冲突的流列表
        """
        conflict_flows = []
        decision_window = 2.0  # 冲突检测时间窗口（秒）
        
        # 收集当前活跃流和即将开始的流的链路占用
        active_links = set()
        future_links = set()
        
        for flow in self.all_known_flows:
            flow_start_time = flow.get("start_time", 0.0)
            flow_status = flow.get("status", "PLANNED")
            flow_path = flow.get("path", [])
            
            # 当前活跃流的链路
            if flow_status == "ACTIVE":
                active_links.update(self._extract_path_links(flow_path))
            
            # 决策窗口内的未来流链路
            elif (flow_status == "PLANNED" and 
                  start_time <= flow_start_time <= start_time + decision_window):
                future_links.update(self._extract_path_links(flow_path))
        
        # 检查每个新流是否与现有流冲突
        for flow in new_flows:
            flow_path = flow.get("path", [])
            flow_links = self._extract_path_links(flow_path)
            
            # 检查与活跃流或未来流的冲突
            if flow_links.intersection(future_links):
                conflict_flows.append(flow)
                logger.debug(f"流 {flow.get('flow_id')} 检测到路径冲突")
        
        return conflict_flows
    
    def _extract_path_links(self, path: List[str]) -> set:
        """
        从路径中提取链路集合
        
        Args:
            path: 路径节点列表
            
        Returns:
            链路集合 {(src, dst), ...}
        """
        links = set()
        if len(path) > 1:
            for i in range(len(path) - 1):
                links.add((path[i], path[i + 1]))
        return links
    
    def _prepare_optimization_flows(self, conflict_flows: List[Dict], start_time: float) -> List[Dict]:
        """
        准备路由优化的输入流集合
        
        Args:
            conflict_flows: 冲突流
            start_time: 开始时间
            
        Returns:
            优化输入流列表
        """
        optimization_flows = []
        decision_window = 2.0
        
        # 添加冲突流
        optimization_flows.extend(conflict_flows)
        
        # 添加相关的背景流和未来流
        for flow in self.all_known_flows:
            flow_start_time = flow.get("start_time", 0.0)
            flow_status = flow.get("status", "PLANNED")
            
            # 活跃流或决策窗口内的未来流
            if (flow_status == "ACTIVE" or 
                (flow_status == "PLANNED" and 
                 start_time <= flow_start_time <= start_time + decision_window)):
                
                # 避免重复添加
                if flow not in optimization_flows:
                    optimization_flows.append(flow)
        
        return optimization_flows
    
    def set_routing_optimizer(self, routing_optimizer):
        """
        设置路由优化器实例
        
        Args:
            routing_optimizer: 路由优化器实例
        """
        self.routing_optimizer = routing_optimizer
        logger.info("动态路由优化器已设置")
    
    def reset_simulation(self):
        """重置模拟器到初始状态"""
        self.event_manager.reset()
        self.tracker.reset()
        self.all_known_flows.clear()
        self.flow_id_to_index.clear()
        self.job_registry.clear()
        
        self.current_interval_start = 0.0
        self.current_interval_duration = 60.0
        self.total_events_processed = 0
        self.total_flows_simulated = 0
        self.simulation_time_elapsed = 0.0
        
        logger.info("通信模拟器已重置")

    def _save_cross_interval_state(self, interval_end_time: float):
        """
        保存跨间隔的状态信息

        Args:
            interval_end_time: 间隔结束时间
        """
        # 计算部分完成的通信时间
        partial_communication_times = {}

        for job_name in self.job_registry:
            # 获取作业在当前间隔内的部分通信时间
            job_start_time = None
            if (job_name in self.tracker.job_round_start_times and
                0 in self.tracker.job_round_start_times[job_name]):
                job_start_time = self.tracker.job_round_start_times[job_name][0]

            if job_start_time is not None and job_start_time < interval_end_time:
                # 计算到间隔结束时的部分通信时间
                partial_time = interval_end_time - job_start_time
                partial_communication_times[job_name] = partial_time
                logger.debug(f"作业 {job_name} 部分通信时间: {partial_time:.4f}秒")
            else:
                partial_communication_times[job_name] = 0.0

        # 将部分时间存储到tracker中
        for job_name, partial_time in partial_communication_times.items():
            if job_name in self.tracker.job_communication_times:
                self.tracker.job_communication_times[job_name] = partial_time

        logger.info(f"保存跨间隔状态: {len(partial_communication_times)} 个作业的部分通信时间")



    def _execute_multiple_allreduces(self, active_jobs: List, interval_duration: float) -> Dict[str, float]:
        """
        执行多次Ring All-Reduce循环

        Args:
            active_jobs: 活跃作业列表
            interval_duration: 间隔时长

        Returns:
            平均通信时间字典
        """
        logger.debug(f"开始多次Ring All-Reduce模拟，作业数量: {len(active_jobs)}")

        # 初始化多次通信状态
        self.completed_allreduces = {}
        self.allreduce_count = {}

        # 为每个作业初始化状态（移除错误的预估逻辑）
        for job in active_jobs:
            self.allreduce_count[job.name] = 0
            self.completed_allreduces[job.name] = []

            logger.debug(f"作业 {job.name}: 开始迭代式多次Ring All-Reduce，基于时间边界动态控制")

        # 启动第一轮Ring All-Reduce
        self._prepare_initial_events(active_jobs)

        # 执行事件循环，支持多次Ring All-Reduce
        self._execute_multiple_allreduce_loop(interval_duration)

        # 计算平均通信时间
        average_times = self._calculate_average_communication_times()

        logger.info(f"多次Ring All-Reduce模拟完成，结果: {average_times}")
        return average_times

    def _execute_multiple_allreduce_loop(self, interval_duration: float) -> None:
        """
        执行多次Ring All-Reduce的事件循环 - 增强启动条件检查版本

        Args:
            interval_duration: 间隔时长
        """
        interval_end_time = self.current_interval_start + interval_duration
        logger.info(f"开始多次Ring All-Reduce事件循环，间隔时间 [{self.current_interval_start:.4f}, {interval_end_time:.4f}]")

        # 关键修复：启动前验证事件队列状态
        initial_event_count = self.event_manager.size()
        if initial_event_count == 0:
            logger.error("❌ 无法启动事件循环：事件队列为空！这表明初始事件创建失败。")
            logger.error("可能原因：1) 流数据丢失 2) 初始化顺序错误 3) 作业配置问题")
            return

        logger.info(f"事件循环启动条件检查通过：{initial_event_count} 个初始事件待处理")

        loop_iteration = 0
        while not self.event_manager.is_empty():
            loop_iteration += 1

            # 获取下一个事件
            next_event = self.event_manager.peek_next_event()

            # 检查是否超出调度间隔
            if next_event.timestamp > interval_end_time:
                logger.info(f"事件时间 {next_event.timestamp:.4f} 超出间隔结束时间 {interval_end_time:.4f}，保存状态并停止模拟")
                self._save_cross_interval_state(interval_end_time)
                break

            # 处理事件
            event = self.event_manager.get_next_event()
            logger.debug(f"循环 {loop_iteration}: 处理事件 {event.event_type.value} @ {event.timestamp:.4f}")

            self._process_event_with_restart_check(event, interval_end_time)
            self.total_events_processed += 1

            # 定期报告进度
            if loop_iteration % 10 == 0:
                remaining_events = self.event_manager.size()
                logger.debug(f"多次Ring All-Reduce循环进度: 已处理 {loop_iteration} 个事件，队列剩余 {remaining_events} 个事件")

        # 事件循环结束后的状态报告
        if loop_iteration == 0:
            logger.error("❌ 事件循环异常：没有处理任何事件！")
        else:
            logger.info(f"✅ 多次Ring All-Reduce事件循环正常结束：总共处理了 {loop_iteration} 个事件")

    def _process_event_with_restart_check(self, event, interval_end_time: float) -> None:
        """
        处理事件并检查是否需要重启Ring All-Reduce

        Args:
            event: 事件对象
            interval_end_time: 间隔结束时间
        """
        # 处理原有事件逻辑
        self._process_event(event)

        # 如果是FINISH_FLOW事件，检查是否需要重启Ring All-Reduce
        if event.event_type == EventType.FINISH_FLOW:
            self._check_and_restart_allreduce(event, interval_end_time)

    def _check_and_restart_allreduce(self, finish_event, interval_end_time: float) -> None:
        """
        检查Ring All-Reduce完成情况并决定是否重启

        Args:
            finish_event: FINISH_FLOW事件
            interval_end_time: 间隔结束时间
        """
        # 正确访问flow_id - 它存储在event.data中
        flow_id = finish_event.data.get("flow_id")
        if not flow_id or flow_id not in self.tracker.flow_to_job:
            return

        job_name = self.tracker.flow_to_job[flow_id]
        job_status = self.tracker.get_job_status(job_name)

        # 检查作业是否完成了一次完整的Ring All-Reduce
        if job_status == JobStatus.COMPLETED:
            self._handle_allreduce_completion(job_name, finish_event.timestamp, interval_end_time)

    def _handle_allreduce_completion(self, job_name: str, completion_time: float, interval_end_time: float) -> None:
        """
        处理Ring All-Reduce完成，基于时间边界动态决定是否继续

        Args:
            job_name: 作业名称
            completion_time: 完成时间
            interval_end_time: 间隔结束时间
        """
        # 记录本次Ring All-Reduce的通信时间
        comm_time = self.tracker.get_job_communication_time(job_name)
        self.completed_allreduces[job_name].append(comm_time)
        self.allreduce_count[job_name] += 1

        current_count = self.allreduce_count[job_name]

        # 动态计算下次开始时间
        next_start_time = completion_time + self._calculate_computation_time(self.job_registry[job_name])

        logger.debug(f"作业 {job_name} 完成第 {current_count} 次Ring All-Reduce，"
                   f"通信时间={comm_time:.4f}秒，下次开始时间={next_start_time:.4f}秒")

        # 纯粹基于时间边界判断是否继续（移除预估次数依赖）
        if next_start_time < interval_end_time:
            logger.debug(f"作业 {job_name} 在时间边界内，启动下一次Ring All-Reduce")
            self._start_new_allreduce_cycle(job_name, next_start_time)
        else:
            logger.info(f"作业 {job_name} 下次开始时间 {next_start_time:.4f} "
                       f"超出间隔结束时间 {interval_end_time:.4f}，停止启动新周期")

    def _start_new_allreduce_cycle(self, job_name: str, start_time: float) -> None:
        """
        启动新的Ring All-Reduce周期（修复版本，解决竞态条件问题）

        Args:
            job_name: 作业名称
            start_time: 开始时间
        """
        job = self.job_registry[job_name]

        # 🔧 修复：精确检查该作业的真正未完成流
        active_job_flows = []
        
        # 方法1：检查Tracker的active_flows（最权威的来源）
        for flow_id, flow_data in self.tracker.active_flows.items():
            if flow_data.get('job_name') == job_name:
                active_job_flows.append(flow_id)
                logger.debug(f"发现Tracker中的活跃流: {flow_id}")
        
        # 方法2：检查事件队列中该作业的待处理流事件
        pending_events = 0
        for event in self.event_manager.event_queue:
            if (event.event_type in [EventType.START_FLOW, EventType.FINISH_FLOW] and
                event.data.get("flow", {}).get("job_name") == job_name):
                flow_id = event.data.get("flow_id")
                if flow_id and flow_id not in active_job_flows:
                    # 检查这是否是一个真正的未完成流
                    if (event.event_type == EventType.START_FLOW or 
                        (event.event_type == EventType.FINISH_FLOW and flow_id in self.tracker.flow_to_job)):
                        active_job_flows.append(flow_id)
                        logger.debug(f"发现事件队列中的待处理流: {flow_id} (事件类型: {event.event_type.value})")
                pending_events += 1
        
        logger.debug(f"作业 {job_name} 状态检查：活跃流={len(active_job_flows)}, 待处理事件={pending_events}")
        
        if active_job_flows:
            # 检查是否需要强制清理（防止无限重试）
            retry_count = getattr(self, f'_retry_count_{job_name}', 0)
            max_retries = 5  # 最多重试5次
            
            if retry_count >= max_retries:
                logger.error(f"💥 作业 {job_name} 重试次数达到上限({max_retries})，强制清理卡住的流并继续")
                # 🔧 完整清理：同步清理tracker、FlowManager和事件队列
                for stuck_flow_id in active_job_flows:
                    logger.warning(f"🧹 强制清理卡住的流: {stuck_flow_id}")
                    
                    # 1. 清理事件队列中的相关事件（防止孤儿事件）
                    removed_events = self.event_manager.remove_events_by_flow(stuck_flow_id)
                    logger.debug(f"清理流 {stuck_flow_id} 的 {removed_events} 个孤儿事件")
                    
                    # 2. 从Tracker中移除映射和状态
                    self.tracker.active_flows.pop(stuck_flow_id, None)
                    self.tracker.flow_to_job.pop(stuck_flow_id, None)
                    self.tracker.flow_to_round.pop(stuck_flow_id, None)
                    self.tracker.completed_flows.pop(stuck_flow_id, None)
                    
                    # 3. 在FlowManager中标记为完成（保持状态一致性）
                    flow = self.flow_manager.get_flow_by_id(stuck_flow_id)
                    if flow:
                        self._update_flow_status(flow, "COMPLETED", start_time)
                        logger.debug(f"✅ 流 {stuck_flow_id} 状态同步完成")
                
                logger.info(f"🧹 强制清理完成：{len(active_job_flows)} 个卡住的流和相关事件已清理")
                # 重置重试计数器
                delattr(self, f'_retry_count_{job_name}')
            else:
                logger.warning(f"⚠️  作业 {job_name} 启动新周期时发现 {len(active_job_flows)} 个未完成流：{active_job_flows[:3]}...")
                logger.warning(f"延迟 {start_time + 0.001:.4f}秒后重试启动新周期，避免竞态条件 (重试 {retry_count + 1}/{max_retries})")
                # 增加重试计数器
                setattr(self, f'_retry_count_{job_name}', retry_count + 1)
                # 延迟重试，给旧流一点时间完成
                self.event_manager.add_event(start_time + 0.001, EventType.RETRY_NEW_CYCLE, {"job_name": job_name})
                return

        # 安全重置作业的追踪状态（确保没有活跃流）
        self.tracker.reset_job_state(job_name)
        
        # 重置重试计数器（成功启动新周期）
        if hasattr(self, f'_retry_count_{job_name}'):
            delattr(self, f'_retry_count_{job_name}')

        # 使用模板生成器生成第一轮流（轮次-1表示生成第0轮）
        first_round_flows = self.flow_generator.generate_next_round_flows(
            job, -1, self.topology_manager
        )

        if first_round_flows:
            # 确保模板实例化的流正确设置了开始时间
            for flow in first_round_flows:
                flow["start_time"] = start_time
                # 确保每个流都在FlowManager中正确注册
                self.flow_manager.add_flow(flow)

            # 重新初始化作业追踪
            ring_size = self._calculate_ring_size(job)
            self.tracker.initialize_job(job, ring_size, first_round_flows)
            self.tracker.start_job_communication(job_name, start_time)

            # 添加第一轮流的START_FLOW事件
            for flow in first_round_flows:
                self.event_manager.add_start_flow_event(start_time, flow)

            logger.debug(f"✅ 作业 {job_name} 安全启动新的Ring All-Reduce周期，"
                       f"开始时间={start_time:.4f}，第一轮流数量={len(first_round_flows)}")
        else:
            logger.warning(f"❌ 作业 {job_name} 无法从模板生成新周期的第一轮流")

    def _calculate_average_communication_times(self) -> Dict[str, float]:
        """
        计算所有作业的平均通信时间

        Returns:
            平均通信时间字典
        """
        average_times = {}

        for job_name, comm_times in self.completed_allreduces.items():
            if comm_times:
                average_time = sum(comm_times) / len(comm_times)
                average_times[job_name] = average_time

                logger.debug(f"作业 {job_name}: 完成 {len(comm_times)} 次Ring All-Reduce，"
                           f"通信时间列表={[f'{t:.4f}' for t in comm_times]}，"
                           f"平均时间={average_time:.4f}秒")
            else:
                # 如果没有完成任何Ring All-Reduce，尝试获取当前进行中的部分时间
                partial_time = self.tracker.get_job_communication_time(job_name)
                average_times[job_name] = partial_time

                logger.warning(f"作业 {job_name}: 未完成任何Ring All-Reduce，"
                              f"使用部分通信时间={partial_time:.4f}秒")

        return average_times
    
    def get_simulation_stats(self) -> Dict[str, Any]:
        """
        获取通信模拟器的统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'total_flows_simulated': self.total_flows_simulated,
            'total_events_processed': self.total_events_processed,
            'active_jobs_count': len(self.tracker.active_jobs) if hasattr(self.tracker, 'active_jobs') else 0,
            'buffer_stats': self.prediction_buffer.get_buffer_stats() if hasattr(self, 'prediction_buffer') else {}
        }
    
    def _calculate_ring_size(self, job) -> int:
        """
        计算作业的Ring大小

        Args:
            job: 作业对象

        Returns:
            Ring大小
        """
        if hasattr(job, 'placement') and job.placement:
            # Ring大小等于跨越的节点数
            return len(job.placement)
        return 1

    def _calculate_computation_time(self, job) -> float:
        """
        计算作业的计算时间（两次通信之间的间隔）
        基于实际的训练特性，而不是预估

        Args:
            job: 作业对象

        Returns:
            计算时间（秒）
        """
        try:
            # 基于作业类型和配置的合理估算
            base_compute_time = self._get_base_compute_time(job)
            return max(base_compute_time, 0.1)  # 最少0.1秒

        except Exception as e:
            logger.warning(f"计算作业 {job.name} 计算时间时出错，使用默认值: {e}")
            return 1.0  # 默认1秒

    def _get_base_compute_time(self, job) -> float:
        """
        基于作业特性估算基础计算时间 - 增强健壮性版本

        Args:
            job: 作业对象

        Returns:
            基础计算时间（秒）
        """
        try:
            # 基于模型类型的计算时间估算
            model_compute_times = {
                'bert': 2.0,        # BERT模型计算较重
                'cifar10': 0.5,     # CIFAR10模型较轻
                'imagenet': 1.5,    # ImageNet模型中等
                'deepspeech2': 1.8, # DeepSpeech2模型较重
                'ncf': 0.3,         # NCF模型很轻
                'yolov3': 1.2       # YOLOv3模型中等
            }

            # 使用多级回退策略获取模型名称
            model_name = self._extract_model_name_for_compute_time(job)
            base_time = model_compute_times.get(model_name, 1.0)

            # 根据GPU数量调整（更多GPU可能意味着更大的批大小，需要更多计算时间）
            if hasattr(job, 'placement') and job.placement:
                total_gpus = sum(job.placement)
                # GPU数量越多，计算时间可能稍微增加
                gpu_factor = 1.0 + (total_gpus - 1) * 0.1
                base_time *= gpu_factor

            logger.debug(f"作业 {job.name} 模型 {model_name}: 基础计算时间 {base_time:.2f}秒")
            return base_time

        except Exception as e:
            logger.warning(f"计算作业 {job.name} 基础计算时间时出错: {e}")
            return 1.0

    def _extract_model_name_for_compute_time(self, job) -> str:
        """
        为计算时间计算提取模型名称（多级回退策略）

        Args:
            job: 作业对象

        Returns:
            模型名称
        """
        # 策略1: 尝试从job.application.name获取
        if hasattr(job, 'application') and hasattr(job.application, 'name'):
            return job.application.name

        # 策略2: 从作业名称中推断模型类型
        job_name = job.name.lower()
        if 'bert' in job_name:
            return 'bert'
        elif 'cifar' in job_name:
            return 'cifar10'
        elif 'imagenet' in job_name:
            return 'imagenet'
        elif 'deepspeech' in job_name:
            return 'deepspeech2'
        elif 'ncf' in job_name:
            return 'ncf'
        elif 'yolo' in job_name:
            return 'yolov3'

        # 策略3: 检查是否有其他模型属性
        if hasattr(job, 'model_name'):
            return job.model_name

        # 策略4: 基于作业名称的模式匹配
        import re
        model_patterns = {
            r'bert.*': 'bert',
            r'cifar.*': 'cifar10',
            r'imagenet.*': 'imagenet',
            r'deepspeech.*': 'deepspeech2',
            r'ncf.*': 'ncf',
            r'yolo.*': 'yolov3'
        }

        for pattern, model in model_patterns.items():
            if re.match(pattern, job_name):
                return model

        # 默认返回通用模型
        logger.debug(f"无法确定作业 {job.name} 的模型类型，使用默认计算时间")
        return 'unknown'

    def set_interval_start_time(self, start_time: float):
        """
        设置当前调度间隔的开始时间
        
        Args:
            start_time: 开始时间
        """
        self.current_interval_start = start_time
        self.event_manager.set_current_time(start_time) 

    def _handle_retry_new_cycle_event(self, event):
        """
        处理重试新周期事件
        
        Args:
            event: RETRY_NEW_CYCLE事件
        """
        job_name = event.data["job_name"]
        retry_time = event.timestamp
        
        logger.debug(f"🔄 处理作业 {job_name} 的重试新周期事件，时间={retry_time:.4f}")
        
        # 重试启动新的Ring All-Reduce周期
        self._start_new_allreduce_cycle(job_name, retry_time)

    def _schedule_next_round_flows_with_dynamic_routing(self, next_round_flows: List[Dict], start_time: float):
        """
        调度下一轮流的START_FLOW事件，并进行动态路由优化
        
        Args:
            next_round_flows: 下一轮流列表
            start_time: 开始时间
        """
        if not next_round_flows:
            return
        
        # 为新流分配候选路径
        for flow in next_round_flows:
            self._assign_candidate_paths(flow)
        
        # 根据标志决定是否执行动态路由优化
        if self.enable_routing_optimizer:
            logger.debug("执行动态路由优化...")
            optimized_routing = self._perform_dynamic_routing_optimization(next_round_flows, start_time)
        else:
            logger.debug("跳过路由优化，使用默认ECMP路径")
            optimized_routing = {}
        
        # 应用优化后的路由并调度流 - 增强流同步版本
        successfully_added = 0
        for flow in next_round_flows:
            flow_id = flow["flow_id"]

            # 关键修复：确保流正确添加到FlowManager
            logger.debug(f"🔍 尝试添加动态流 {flow_id}，状态: {flow.get('status')}, 轮次: {flow.get('round_idx')}")
            if self._add_flow_to_known_flows_with_verification(flow):
                # 应用优化后的路由
                if flow_id in optimized_routing:
                    flow["path"] = optimized_routing[flow_id]
                    logger.debug(f"流 {flow_id} 应用优化路由: {optimized_routing[flow_id]}")

                # 设置开始时间
                flow["start_time"] = start_time

                # 添加START_FLOW事件
                self.event_manager.add_start_flow_event(start_time, flow)
                successfully_added += 1
                logger.debug(f"✅ 动态流 {flow_id} 成功调度，START_FLOW事件已添加")
            else:
                logger.error(f"❌ 动态流 {flow_id} 添加到FlowManager失败，跳过调度")

        logger.info(f"动态流调度完成: {successfully_added}/{len(next_round_flows)} 个流成功添加并调度")
        
        if self.enable_routing_optimizer:
            logger.debug(f"调度了 {len(next_round_flows)} 个下一轮流，开始时间={start_time:.4f}，应用动态路由优化")
        else:
            logger.debug(f"调度了 {len(next_round_flows)} 个下一轮流，开始时间={start_time:.4f}，使用默认ECMP路径")
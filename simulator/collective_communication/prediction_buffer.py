#!/usr/bin/env python3
"""
预测缓冲器 - 批量预测系统核心组件
将多个流的预测请求聚合为批量调用，提高模型预测效率
"""

import time
import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

class PredictionBuffer:
    """批量预测缓冲器"""
    
    def __init__(self, buffer_size_limit: int = 50, time_window: float = 0.1, prediction_service = None):
        """
        初始化预测缓冲器
        
        Args:
            buffer_size_limit: 缓冲区最大容量限制
            time_window: 时间窗口阈值（秒）
            prediction_service: 预测服务实例引用
        """
        self.pending_flows: List[Dict] = []  # 待预测流缓冲队列
        self.buffer_size_limit = buffer_size_limit  # 缓冲区最大容量限制
        self.time_window = time_window  # 时间窗口阈值（秒）
        self.last_flush_time = 0.0  # 上次刷新时间戳
        self.prediction_service = prediction_service  # 预测服务实例引用
        
        # 统计信息字典
        self.stats = {
            'total_flows': 0,
            'total_batches': 0,
            'total_flush_calls': 0,
            'max_batch_size': 0,
            'time_window_flushes': 0,
            'size_limit_flushes': 0
        }
        
        logger.debug(f"PredictionBuffer初始化完成: buffer_size_limit={buffer_size_limit}, time_window={time_window}")
    
    def add_flow_for_prediction(self, flow: Dict, current_time: float) -> None:
        """
        添加流到预测缓冲区
        
        Args:
            flow: 流数据字典
            current_time: 当前时间
        """
        self.pending_flows.append(flow)
        self.stats['total_flows'] += 1
        
        # 更新首次添加时间
        if len(self.pending_flows) == 1:
            self.last_flush_time = current_time
        
        logger.debug(f"流 {flow.get('flow_id', 'unknown')} 已添加到预测缓冲区，当前缓冲区大小: {len(self.pending_flows)}")
    
    def should_flush_buffer(self, current_time: float) -> bool:
        """
        判断是否应该刷新缓冲区
        
        Args:
            current_time: 当前时间
            
        Returns:
            bool: 是否需要刷新
        """
        if not self.pending_flows:
            return False
        
        # 刷新条件1: 达到缓冲区大小限制
        if len(self.pending_flows) >= self.buffer_size_limit:
            logger.debug(f"触发大小限制刷新: {len(self.pending_flows)} >= {self.buffer_size_limit}")
            return True
        
        # 刷新条件2: 超过时间窗口
        time_elapsed = current_time - self.last_flush_time
        if time_elapsed >= self.time_window:
            logger.debug(f"触发时间窗口刷新: {time_elapsed:.4f}s >= {self.time_window}s")
            return True
        
        return False
    
    def flush_and_predict(self, prediction_service, all_known_flows: List[Dict]) -> Dict[str, float]:
        """
        刷新缓冲区并执行批量预测
        
        Args:
            prediction_service: 预测服务
            all_known_flows: 所有已知流
            
        Returns:
            Dict[str, float]: {flow_id: predicted_fct}
        """
        if not self.pending_flows:
            return {}
        
        batch_size = len(self.pending_flows)
        logger.debug(f"执行批量预测: {batch_size} 个流")
        
        try:
            # 执行批量预测
            start_time = time.time()
            predicted_fcts = prediction_service.predict_fct(self.pending_flows, all_known_flows)
            prediction_time = time.time() - start_time
            
            # 构建结果字典
            results = {}
            for i, flow in enumerate(self.pending_flows):
                flow_id = flow.get('flow_id', f'unknown_{i}')
                if i < len(predicted_fcts):
                    results[flow_id] = predicted_fcts[i]
                else:
                    results[flow_id] = 1.0  # 默认值
            
            # 更新统计信息
            self.stats['total_batches'] += 1
            self.stats['total_flush_calls'] += 1
            self.stats['max_batch_size'] = max(self.stats['max_batch_size'], batch_size)
            
            # 区分刷新触发原因
            if batch_size >= self.buffer_size_limit:
                self.stats['size_limit_flushes'] += 1
            else:
                self.stats['time_window_flushes'] += 1
            
            logger.info(f"批量预测完成: {batch_size} 个流, 耗时: {prediction_time:.4f}秒")
            
            # 清空缓冲区
            self.pending_flows.clear()
            
            return results
            
        except Exception as e:
            logger.error(f"批量预测失败: {e}")
            # 返回默认值避免中断
            results = {}
            for flow in self.pending_flows:
                flow_id = flow.get('flow_id', 'unknown')
                results[flow_id] = 1.0
            
            self.pending_flows.clear()
            return results
    
    def get_pending_flow_count(self) -> int:
        """
        获取待预测流数量
        
        Returns:
            int: pending_flows列表长度
        """
        return len(self.pending_flows)
    
    def get_buffer_stats(self) -> Dict:
        """
        获取缓冲区统计信息
        
        Returns:
            Dict: 包含统计数据的字典
        """
        avg_batch_size = (self.stats['total_flows'] / self.stats['total_batches'] 
                         if self.stats['total_batches'] > 0 else 0.0)
        
        return {
            'total_flows': self.stats['total_flows'],
            'total_batches': self.stats['total_batches'],
            'total_flush_calls': self.stats['total_flush_calls'],
            'avg_batch_size': avg_batch_size,
            'max_batch_size': self.stats['max_batch_size'],
            'pending_flows': len(self.pending_flows),
            'time_window_flushes': self.stats['time_window_flushes'],
            'size_limit_flushes': self.stats['size_limit_flushes'],
            'buffer_size_limit': self.buffer_size_limit,
            'time_window': self.time_window
        } 
#!/usr/bin/env python3
"""
分析模型调用次数和性能的脚本
"""

import sys
import os
import time
import math
from typing import Dict, List

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from routing.prediction_service import PredictionService

def analyze_single_model_call_time():
    """分析单次模型调用的时间"""
    print("=== 🔬 单次模型调用时间分析 ===")
    
    prediction_service = PredictionService()
    
    # 创建单个测试流
    test_flow = {
        "flow_id": "test_flow_1",
        "ringallreduce_group_size": 4,
        "flow_features": [10.0],
        "path": ["H1", "S1", "P1", "S2", "H2"],
        "start_time": 0.0,
        "model": "cifar10",
        "dataset": "test",
        "parameters": 1000000.0,
        "status": "PLANNED",
        "job_name": "test_job",
        "round_idx": 0
    }
    
    # 预热
    prediction_service.predict_fct([test_flow], [test_flow])
    
    # 测试单次调用时间
    num_runs = 20
    times = []
    
    for i in range(num_runs):
        start_time = time.time()
        result = prediction_service.predict_fct([test_flow], [test_flow])
        end_time = time.time()
        times.append(end_time - start_time)
    
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    
    print(f"📊 单次模型调用统计 ({num_runs} 次运行):")
    print(f"  ⏱️  平均时间: {avg_time:.6f} 秒")
    print(f"  ⏱️  最短时间: {min_time:.6f} 秒")
    print(f"  ⏱️  最长时间: {max_time:.6f} 秒")
    print(f"  🚀 调用吞吐量: {1/avg_time:.1f} 调用/秒")
    
    return avg_time

def analyze_batch_vs_single_performance():
    """分析批量vs单个预测的性能差异"""
    print("\n=== ⚡ 批量 vs 单个预测性能对比 ===")
    
    prediction_service = PredictionService()
    
    # 创建测试流
    def create_test_flows(num_flows):
        flows = []
        for i in range(num_flows):
            flow = {
                "flow_id": f"test_flow_{i}",
                "ringallreduce_group_size": 4,
                "flow_features": [10.0 + i],
                "path": ["H1", "S1", "P1", "S2", f"H{i%8+1}"],
                "start_time": float(i * 2),
                "model": "cifar10",
                "dataset": "test",
                "parameters": 1000000.0,
                "status": "PLANNED",
                "job_name": f"job_{i%3}",
                "round_idx": 0
            }
            flows.append(flow)
        return flows
    
    batch_sizes = [1, 5, 10, 20, 50]
    results = {}
    
    for batch_size in batch_sizes:
        test_flows = create_test_flows(batch_size)
        
        # 预热
        prediction_service.predict_fct(test_flows[:1], test_flows)
        
        # 测试批量预测
        num_runs = 5
        batch_times = []
        
        for run in range(num_runs):
            start_time = time.time()
            predictions = prediction_service.predict_fct(test_flows, test_flows)
            end_time = time.time()
            batch_times.append(end_time - start_time)
        
        avg_batch_time = sum(batch_times) / len(batch_times)
        per_flow_time = avg_batch_time / batch_size
        throughput = batch_size / avg_batch_time
        
        results[batch_size] = {
            'batch_time': avg_batch_time,
            'per_flow_time': per_flow_time,
            'throughput': throughput
        }
        
        print(f"📦 批量大小 {batch_size:2d}: "
              f"总时间 {avg_batch_time:.4f}s, "
              f"每流 {per_flow_time:.6f}s, "
              f"吞吐量 {throughput:.1f} 流/秒")
    
    # 分析批量优化效果
    print(f"\n💡 批量优化分析:")
    baseline_per_flow = results[1]['per_flow_time']
    for batch_size in batch_sizes[1:]:
        speedup = baseline_per_flow / results[batch_size]['per_flow_time']
        print(f"  批量大小 {batch_size:2d}: {speedup:.2f}x 加速")
    
    return results

def estimate_model_calls_in_simulation():
    """估算仿真中的模型调用次数"""
    print("\n=== 🧮 仿真中模型调用次数估算 ===")
    
    # 基于观察到的数据估算
    jobs_per_interval = [
        {"name": "cifar10-0", "placement": (4, 4, 4, 4), "allreduce_count": 47},
        {"name": "cifar10-1", "placement": (4, 4, 4, 4), "allreduce_count": 68},
        {"name": "cifar10-3", "placement": (4, 4), "allreduce_count": 20},  # 估算
        {"name": "deepspeech2-2", "placement": (2,), "allreduce_count": 0},  # 单节点
        {"name": "bert-4", "placement": (1,), "allreduce_count": 0}  # 单节点
    ]
    
    total_model_calls = 0
    
    print("📋 每个作业的模型调用分析:")
    for job in jobs_per_interval:
        # 只有多节点作业需要模型预测
        if len([x for x in job["placement"] if x > 0]) > 1:
            flows_per_allreduce = sum(job["placement"])
            model_calls = job["allreduce_count"] * flows_per_allreduce
            total_model_calls += model_calls
            
            print(f"  🎯 {job['name']:12s}: "
                  f"{job['allreduce_count']:2d} 次AllReduce × "
                  f"{flows_per_allreduce} 流 = "
                  f"{model_calls:3d} 次模型调用")
        else:
            print(f"  🎯 {job['name']:12s}: 单节点作业，无需模型预测")
    
    print(f"\n📊 单个调度间隔 (60秒) 统计:")
    print(f"  🔥 总模型调用次数: {total_model_calls}")
    print(f"  ⏱️  平均调用频率: {total_model_calls/60:.1f} 调用/秒")
    
    return total_model_calls

def analyze_current_architecture_problems():
    """分析当前架构问题"""
    print("\n=== 🚨 当前架构问题分析 ===")
    
    problems = [
        {
            "问题": "单流预测瓶颈",
            "描述": "每次只预测一个流，无法利用批量计算优势",
            "影响": "GPU利用率低，模型调用开销大"
        },
        {
            "问题": "重复特征计算",
            "描述": "相同时间的全局拓扑特征被重复计算",
            "影响": "计算资源浪费，特征工程开销高"
        },
        {
            "问题": "预测缓冲未实现",
            "描述": "prediction_buffer.py为空，批量预测被禁用",
            "影响": "无法聚合同时间窗口的预测请求"
        },
        {
            "问题": "模型加载开销",
            "描述": "每次调用都重新计算完整的神经网络前向传播",
            "影响": "固定开销占比高，小批量效率低"
        }
    ]
    
    for i, problem in enumerate(problems, 1):
        print(f"❌ 问题 {i}: {problem['问题']}")
        print(f"   📝 描述: {problem['描述']}")
        print(f"   💥 影响: {problem['影响']}")
        print()

def analyze_batch_prediction_impact():
    """分析批量预测的影响"""
    print("=== 🔮 批量预测改进分析 ===")
    
    print("🏗️  架构变更分析:")
    print("  ✅ 无需修改模型架构 - STGNNModel已支持批量输入")
    print("  ✅ 无需修改模型权重 - 使用相同的预训练模型")
    print("  ✅ 仅需实现PredictionBuffer类和调用逻辑")
    
    print("\n⚡ 性能提升潜力:")
    single_call_time = 0.013  # 从性能测试中获得的数据
    batch_call_time_50 = 0.496  # 批量50的时间
    
    # 假设每间隔460次调用（基于之前的估算）
    calls_per_interval = 460
    
    # 当前性能
    current_total_time = calls_per_interval * single_call_time
    
    # 批量预测性能（假设批量大小为20）
    batch_size = 20
    num_batches = math.ceil(calls_per_interval / batch_size)
    batch_total_time = num_batches * (batch_call_time_50 * batch_size / 50)  # 估算批量20的时间
    
    speedup = current_total_time / batch_total_time
    
    print(f"  📈 当前方式: {calls_per_interval} × {single_call_time:.6f}s = {current_total_time:.3f}s")
    print(f"  📈 批量方式: {num_batches} × {batch_total_time/num_batches:.6f}s = {batch_total_time:.3f}s")
    print(f"  🚀 性能提升: {speedup:.1f}x 加速")
    print(f"  💰 时间节省: {current_total_time - batch_total_time:.3f}s ({(1-batch_total_time/current_total_time)*100:.1f}%)")

if __name__ == "__main__":
    print("🔍 开始分析模型调用性能...")
    
    # 1. 分析单次调用时间
    single_call_time = analyze_single_model_call_time()
    
    # 2. 分析批量性能
    batch_results = analyze_batch_vs_single_performance()
    
    # 3. 估算仿真中的调用次数
    total_calls = estimate_model_calls_in_simulation()
    
    # 4. 分析架构问题
    analyze_current_architecture_problems()
    
    # 5. 分析批量预测影响
    analyze_batch_prediction_impact()
    
    print("\n✅ 分析完成！") 
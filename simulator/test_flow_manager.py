#!/usr/bin/env python3
"""
FlowManager和阶段2优化测试脚本
验证数据结构优化的正确性和性能提升
"""

import sys
import time
import logging
from typing import List, Dict

# 设置路径
sys.path.append('.')

from collective_communication.flow_manager import FlowManager
from collective_communication.communication_simulator import CommunicationSimulator
from collective_communication.flow_generator import FlowGenerator
from routing.topology_manager import TopologyManager
from routing.prediction_service import PredictionService

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_flows(job_name: str, total_rounds: int, flows_per_round: int) -> List[Dict]:
    """创建测试流数据"""
    flows = []
    
    for round_idx in range(total_rounds):
        for flow_idx in range(flows_per_round):
            flow = {
                'flow_id': f'{job_name}_round{round_idx}_flow{flow_idx}',
                'job_name': job_name,
                'round_idx': round_idx,
                'status': 'PLANNED',
                'start_time': round_idx * 2.0 + flow_idx * 0.001,
                'flow_features': [10.0],
                'path': ['H1', 'S1', 'P1', 'S2', 'H2'],
                'model': 'test_model',
                'dataset': 'test_dataset',
                'parameters': 1000000.0,
                'predicted_fct': 1.5  # 预设FCT
            }
            flows.append(flow)
    
    return flows

def test_flow_manager_basic_operations():
    """测试FlowManager基本操作"""
    print("=== FlowManager基本操作测试 ===")
    
    flow_manager = FlowManager()
    
    # 创建测试数据
    test_flows = create_test_flows("test_job", 3, 4)
    print(f"创建测试流: {len(test_flows)} 个")
    
    # 测试批量添加
    start_time = time.time()
    added_count = flow_manager.add_flows_batch(test_flows)
    add_time = time.time() - start_time
    
    print(f"批量添加: {added_count} 个流，耗时 {add_time*1000:.2f}ms")
    
    # 测试O(1)查找
    start_time = time.time()
    iterations = 1000
    for _ in range(iterations):
        flow = flow_manager.get_flow_by_id("test_job_round1_flow2")
    lookup_time = time.time() - start_time
    
    print(f"O(1)查找测试: {iterations} 次查找，耗时 {lookup_time*1000:.2f}ms")
    print(f"平均查找时间: {lookup_time/iterations*1000000:.2f}μs")
    
    # 测试状态分类
    active_flows = flow_manager.get_flows_by_status('PLANNED')
    print(f"PLANNED状态流数量: {len(active_flows)}")
    
    # 测试状态更新
    flow_manager.update_flow_status("test_job_round0_flow0", "ACTIVE")
    flow_manager.update_flow_status("test_job_round0_flow1", "COMPLETED")
    
    planned_flows = flow_manager.get_flows_by_status('PLANNED')
    active_flows = flow_manager.get_flows_by_status('ACTIVE')
    completed_flows = flow_manager.get_flows_by_status('COMPLETED')
    
    print(f"状态更新后 - PLANNED: {len(planned_flows)}, ACTIVE: {len(active_flows)}, COMPLETED: {len(completed_flows)}")
    
    # 测试批量删除
    start_time = time.time()
    removed_count = flow_manager.batch_remove_flows("test_job", 0, "COMPLETED")
    delete_time = time.time() - start_time
    
    print(f"批量删除: {removed_count} 个流，耗时 {delete_time*1000:.2f}ms")
    print(f"删除后剩余流数: {flow_manager.get_flow_count()}")
    
    # 显示统计信息
    stats = flow_manager.get_statistics()
    print(f"FlowManager统计: {stats}")

def test_communication_simulator_integration():
    """测试CommunicationSimulator集成"""
    print("\n=== CommunicationSimulator集成测试 ===")
    
    # 创建模拟器组件
    flow_generator = FlowGenerator()
    topology_manager = TopologyManager()
    prediction_service = PredictionService()
    
    comm_simulator = CommunicationSimulator(
        flow_generator, topology_manager, prediction_service
    )
    
    # 创建测试数据
    test_flows = create_test_flows("integration_job", 4, 3)
    print(f"创建集成测试流: {len(test_flows)} 个")
    
    # 测试set_all_known_flows
    start_time = time.time()
    comm_simulator.set_all_known_flows(test_flows)
    set_time = time.time() - start_time
    
    print(f"set_all_known_flows耗时: {set_time*1000:.2f}ms")
    print(f"设置后流数量: {len(comm_simulator.all_known_flows)}")
    
    # 测试向后兼容性
    flow_count_property = len(comm_simulator.all_known_flows)
    flow_count_manager = comm_simulator.flow_manager.get_flow_count()
    
    print(f"向后兼容性检查 - 属性: {flow_count_property}, 管理器: {flow_count_manager}")
    
    if flow_count_property == flow_count_manager:
        print("✅ 向后兼容性正常")
    else:
        print("❌ 向后兼容性异常")
    
    # 测试批量删除集成
    for round_idx in range(2):
        # 标记流为已完成
        for flow in comm_simulator.all_known_flows:
            if (flow.get('job_name') == 'integration_job' and 
                flow.get('round_idx') == round_idx):
                flow['status'] = 'COMPLETED'
                comm_simulator.flow_manager.update_flow_status(flow['flow_id'], 'COMPLETED')
        
        # 触发批量删除
        if round_idx > 0:
            removed_count = comm_simulator._batch_remove_completed_flows('integration_job', round_idx - 1)
            print(f"轮次 {round_idx} 删除了 {removed_count} 个流")
    
    print(f"最终流数量: {len(comm_simulator.all_known_flows)}")

def test_performance_comparison():
    """测试性能对比"""
    print("\n=== 性能对比测试 ===")
    
    # 创建大规模测试数据
    large_flows = []
    for job_idx in range(5):  # 5个作业
        job_flows = create_test_flows(f"perf_job_{job_idx}", 6, 4)  # 每个作业6轮，每轮4流
        large_flows.extend(job_flows)
    
    print(f"创建大规模测试数据: {len(large_flows)} 个流")
    
    # 测试FlowManager性能
    flow_manager = FlowManager()
    
    # 批量添加性能
    start_time = time.time()
    flow_manager.add_flows_batch(large_flows)
    add_time = time.time() - start_time
    
    print(f"FlowManager批量添加: {add_time*1000:.2f}ms")
    
    # 查找性能测试
    start_time = time.time()
    iterations = 10000
    for i in range(iterations):
        flow_id = f"perf_job_{i % 5}_round{(i // 5) % 6}_flow{(i // 30) % 4}"
        flow = flow_manager.get_flow_by_id(flow_id)
    lookup_time = time.time() - start_time
    
    print(f"查找性能: {iterations} 次查找，耗时 {lookup_time*1000:.2f}ms")
    print(f"平均查找时间: {lookup_time/iterations*1000000:.2f}μs")
    
    # 状态过滤性能
    start_time = time.time()
    for _ in range(100):
        planned_flows = flow_manager.get_flows_by_status('PLANNED')
    filter_time = time.time() - start_time
    
    print(f"状态过滤性能: 100次过滤，耗时 {filter_time*1000:.2f}ms")
    
    # 批量删除性能
    # 先标记一些流为已完成
    for i, flow in enumerate(large_flows[:60]):  # 标记前60个流为已完成
        flow['status'] = 'COMPLETED'
        flow_manager.update_flow_status(flow['flow_id'], 'COMPLETED')
    
    start_time = time.time()
    removed_count = flow_manager.batch_remove_flows("perf_job_0", 0, "COMPLETED")
    delete_time = time.time() - start_time
    
    print(f"批量删除性能: 删除 {removed_count} 个流，耗时 {delete_time*1000:.2f}ms")
    
    # 内存使用统计
    final_count = flow_manager.get_flow_count()
    memory_saving = (len(large_flows) - final_count) / len(large_flows) * 100
    print(f"内存节省: {memory_saving:.1f}% ({len(large_flows)} -> {final_count})")

def test_advanced_features():
    """测试高级功能"""
    print("\n=== 高级功能测试 ===")
    
    flow_manager = FlowManager()
    test_flows = create_test_flows("advanced_job", 3, 3)
    flow_manager.add_flows_batch(test_flows)
    
    # 测试时间窗口查找
    start_time = time.time()
    flows_in_window = flow_manager.get_flows_in_time_window(1.0, 3.0)
    window_time = time.time() - start_time
    
    print(f"时间窗口查找: 找到 {len(flows_in_window)} 个流，耗时 {window_time*1000:.2f}ms")
    
    # 测试同步流查找
    target_flow = test_flows[0]
    start_time = time.time()
    sync_flows = flow_manager.get_synchronous_flows(target_flow)
    sync_time = time.time() - start_time
    
    print(f"同步流查找: 找到 {len(sync_flows)} 个同步流，耗时 {sync_time*1000:.2f}ms")
    
    # 测试活跃流查找
    start_time = time.time()
    active_flows = flow_manager.get_active_flows_at_time(2.0)
    active_time = time.time() - start_time
    
    print(f"活跃流查找: 找到 {len(active_flows)} 个活跃流，耗时 {active_time*1000:.2f}ms")

if __name__ == "__main__":
    test_flow_manager_basic_operations()
    test_communication_simulator_integration()
    test_performance_comparison()
    test_advanced_features()
    print("\n✅ FlowManager和阶段2优化测试完成")

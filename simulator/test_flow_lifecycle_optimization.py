#!/usr/bin/env python3
"""
流生命周期管理优化测试
验证流恢复-删除循环问题的修复效果
"""

import sys
import time
import logging
from typing import List, Dict

# 设置路径
sys.path.append('.')

from collective_communication.communication_simulator import CommunicationSimulator
from collective_communication.flow_generator import FlowGenerator
from collective_communication.flow_manager import FlowManager
from routing.topology_manager import TopologyManager
from routing.prediction_service import PredictionService

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockJob:
    """模拟作业对象"""
    def __init__(self, name: str, num_replicas: int = 4):
        self.name = name
        self.num_replicas = num_replicas
        self.placement = tuple(1 for _ in range(num_replicas))
        self.atomic_bsz = 32
        self.accum_steps = 1
        self.profile = {
            'compute_time': 1.0,
            'sync_time': 0.1
        }

def test_flow_reference_counting():
    """测试流引用计数机制"""
    print("=== 流引用计数机制测试 ===")
    
    flow_manager = FlowManager()
    
    # 创建测试流
    test_flow = {
        'flow_id': 'test_ref_flow',
        'job_name': 'test_job',
        'round_idx': 0,
        'status': 'ACTIVE',
        'flow_features': [5.0]
    }
    
    # 添加流
    success = flow_manager.add_flow(test_flow)
    print(f"添加流: {'成功' if success else '失败'}")
    
    # 检查初始引用计数
    initial_ref = flow_manager.get_flow_reference_count('test_ref_flow')
    print(f"初始引用计数: {initial_ref}")
    
    # 获取额外引用
    ref1 = flow_manager.acquire_flow_reference('test_ref_flow')
    ref2 = flow_manager.acquire_flow_reference('test_ref_flow')
    current_ref = flow_manager.get_flow_reference_count('test_ref_flow')
    print(f"获取2个额外引用后: {current_ref}")
    
    # 释放引用
    flow_manager.release_flow_reference('test_ref_flow')
    flow_manager.release_flow_reference('test_ref_flow')
    final_ref = flow_manager.get_flow_reference_count('test_ref_flow')
    print(f"释放2个引用后: {final_ref}")
    
    # 验证结果
    if initial_ref == 1 and current_ref == 3 and final_ref == 1:
        print("✅ 流引用计数机制正常工作")
        return True
    else:
        print("❌ 流引用计数机制异常")
        return False

def test_flow_usage_state_management():
    """测试流使用状态管理"""
    print("\n=== 流使用状态管理测试 ===")
    
    flow_manager = FlowManager()
    
    # 创建测试流
    test_flow = {
        'flow_id': 'test_state_flow',
        'job_name': 'test_job',
        'round_idx': 0,
        'status': 'PLANNED',
        'flow_features': [5.0]
    }
    
    # 添加流
    flow_manager.add_flow(test_flow)
    
    # 检查初始状态
    initial_state = flow_manager.get_flow_usage_state('test_state_flow')
    print(f"初始使用状态: {initial_state}")
    
    # 更新状态
    states_to_test = ['PROCESSING', 'COMPLETED', 'DELETING']
    for state in states_to_test:
        success = flow_manager.set_flow_usage_state('test_state_flow', state)
        current_state = flow_manager.get_flow_usage_state('test_state_flow')
        print(f"设置状态 {state}: {'成功' if success else '失败'}, 当前状态: {current_state}")
    
    # 验证结果
    final_state = flow_manager.get_flow_usage_state('test_state_flow')
    if final_state == 'DELETING':
        print("✅ 流使用状态管理正常工作")
        return True
    else:
        print("❌ 流使用状态管理异常")
        return False

def test_intelligent_flow_recovery():
    """测试智能流恢复判断"""
    print("\n=== 智能流恢复判断测试 ===")
    
    # 创建模拟器
    flow_generator = FlowGenerator()
    topology_manager = TopologyManager()
    prediction_service = PredictionService()
    
    comm_simulator = CommunicationSimulator(
        flow_generator, topology_manager, prediction_service
    )
    
    # 创建测试作业
    test_job = MockJob("recovery-test-job", num_replicas=2)
    comm_simulator.job_registry[test_job.name] = test_job
    
    # 测试不同的流ID
    test_cases = [
        ("recovery-test-job_round0_srcH1_dstH2", True, "正常流ID"),
        ("invalid_format", False, "无效格式"),
        ("nonexistent-job_round0_srcH1_dstH2", False, "不存在的作业"),
    ]
    
    results = []
    for flow_id, expected, description in test_cases:
        should_recover = comm_simulator._should_attempt_flow_recovery(flow_id)
        result = should_recover == expected
        results.append(result)
        status = "✅" if result else "❌"
        print(f"{status} {description}: 预期 {expected}, 实际 {should_recover}")

    # 单独测试重复恢复限制
    flow_id_for_repeat = "recovery-test-job_round0_srcH1_dstH2"

    # 第二次调用同一个流ID，应该被限制
    should_recover_repeat = comm_simulator._should_attempt_flow_recovery(flow_id_for_repeat)
    repeat_result = not should_recover_repeat  # 期望为False，所以取反
    results.append(repeat_result)

    status = "✅" if repeat_result else "❌"
    print(f"{status} 重复恢复（应被限制）: 预期 False, 实际 {should_recover_repeat}")

    if all(results):
        print("✅ 智能流恢复判断正常工作")
        return True
    else:
        print("❌ 智能流恢复判断存在问题")
        return False

def test_safe_batch_deletion():
    """测试安全批量删除"""
    print("\n=== 安全批量删除测试 ===")
    
    flow_manager = FlowManager()
    
    # 创建测试流
    test_flows = []
    for i in range(4):
        flow = {
            'flow_id': f'batch_test_round0_flow{i}',
            'job_name': 'batch_test',
            'round_idx': 0,
            'status': 'COMPLETED',
            'flow_features': [5.0]
        }
        test_flows.append(flow)
        flow_manager.add_flow(flow)
    
    print(f"创建了 {len(test_flows)} 个测试流")
    
    # 为部分流增加引用
    flow_manager.acquire_flow_reference('batch_test_round0_flow0')
    flow_manager.acquire_flow_reference('batch_test_round0_flow1')
    
    print("为前2个流增加了额外引用")
    
    # 尝试批量删除
    deleted_count = flow_manager.batch_remove_flows('batch_test', 0, 'COMPLETED')
    remaining_count = flow_manager.get_flow_count()
    
    print(f"批量删除结果: 删除了 {deleted_count} 个流，剩余 {remaining_count} 个流")
    
    # 验证结果：应该只删除没有额外引用的流
    if deleted_count == 2 and remaining_count == 2:
        print("✅ 安全批量删除正常工作：有引用的流被保护")
        return True
    else:
        print("❌ 安全批量删除异常")
        return False

def test_recovery_deletion_cycle_prevention():
    """测试恢复-删除循环预防"""
    print("\n=== 恢复-删除循环预防测试 ===")
    
    # 创建模拟器
    flow_generator = FlowGenerator()
    topology_manager = TopologyManager()
    prediction_service = PredictionService()
    
    comm_simulator = CommunicationSimulator(
        flow_generator, topology_manager, prediction_service
    )
    
    # 创建测试作业
    test_job = MockJob("cycle-test-job", num_replicas=2)
    comm_simulator.job_registry[test_job.name] = test_job
    
    # 模拟已完成的轮次
    comm_simulator.completed_rounds_tracker[test_job.name] = {0}
    
    # 测试尝试恢复已完成轮次的流
    flow_id = "cycle-test-job_round0_srcH1_dstH2"
    
    # 第一次尝试恢复
    should_recover1 = comm_simulator._should_attempt_flow_recovery(flow_id)
    print(f"第一次恢复判断: {should_recover1}")
    
    # 由于轮次已完成，应该不允许恢复
    if not should_recover1:
        print("✅ 恢复-删除循环预防正常工作：已完成轮次的流不会被恢复")
        return True
    else:
        print("❌ 恢复-删除循环预防异常：已完成轮次的流仍可能被恢复")
        return False

def test_flow_lifecycle_statistics():
    """测试流生命周期统计"""
    print("\n=== 流生命周期统计测试 ===")
    
    flow_manager = FlowManager()
    
    # 创建不同状态的流
    test_flows = [
        {'flow_id': 'stat_flow1', 'job_name': 'stat_job', 'status': 'PLANNED'},
        {'flow_id': 'stat_flow2', 'job_name': 'stat_job', 'status': 'ACTIVE'},
        {'flow_id': 'stat_flow3', 'job_name': 'stat_job', 'status': 'COMPLETED'},
    ]
    
    for flow in test_flows:
        flow_manager.add_flow(flow)
    
    # 设置不同的使用状态
    flow_manager.set_flow_usage_state('stat_flow1', 'ACTIVE')
    flow_manager.set_flow_usage_state('stat_flow2', 'PROCESSING')
    flow_manager.set_flow_usage_state('stat_flow3', 'COMPLETED')
    
    # 获取统计信息
    stats = flow_manager.get_statistics()
    
    print("流生命周期统计:")
    print(f"  当前流数量: {stats['current_flows']}")
    print(f"  总引用数: {stats['total_references']}")
    print(f"  有引用的流数: {stats['flows_with_references']}")
    print(f"  使用状态分布: {stats['usage_states']}")
    
    # 验证统计的正确性
    expected_flows = 3
    expected_refs = 3  # 每个流初始引用为1
    
    if (stats['current_flows'] == expected_flows and 
        stats['total_references'] == expected_refs):
        print("✅ 流生命周期统计正常工作")
        return True
    else:
        print("❌ 流生命周期统计异常")
        return False

if __name__ == "__main__":
    print("开始流生命周期管理优化测试...\n")
    
    # 运行所有测试
    test_results = []
    
    test_results.append(("流引用计数机制", test_flow_reference_counting()))
    test_results.append(("流使用状态管理", test_flow_usage_state_management()))
    test_results.append(("智能流恢复判断", test_intelligent_flow_recovery()))
    test_results.append(("安全批量删除", test_safe_batch_deletion()))
    test_results.append(("恢复-删除循环预防", test_recovery_deletion_cycle_prevention()))
    test_results.append(("流生命周期统计", test_flow_lifecycle_statistics()))
    
    # 总结结果
    print("\n" + "="*50)
    print("流生命周期优化测试结果总结:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有流生命周期优化测试通过！恢复-删除循环问题已修复。")
    else:
        print("\n⚠️  部分测试失败，需要进一步调试。")

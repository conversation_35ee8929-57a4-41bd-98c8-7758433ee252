#!/usr/bin/env python3
"""
简单的FlowGenerator修复测试 - 不依赖外部模块
"""

import sys
import os

# 添加simulator目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_flow_generator_method_exists():
    """测试FlowGenerator是否有正确的方法"""
    print("=== FlowGenerator方法存在性测试 ===")
    
    try:
        from collective_communication.flow_generator import FlowGenerator
        
        flow_generator = FlowGenerator()
        
        # 检查方法是否存在
        methods_to_check = [
            'generate_all_potential_flows',
            'generate_next_round_flows',
            '_generate_job_flows',
            '_generate_round_flows'
        ]
        
        print("检查FlowGenerator方法:")
        for method_name in methods_to_check:
            if hasattr(flow_generator, method_name):
                print(f"  ✅ {method_name} 方法存在")
            else:
                print(f"  ❌ {method_name} 方法不存在")
                return False
        
        # 检查不应该存在的方法
        if hasattr(flow_generator, 'generate_first_round_flows'):
            print(f"  ❌ generate_first_round_flows 方法不应该存在")
            return False
        else:
            print(f"  ✅ generate_first_round_flows 方法正确地不存在")
        
        print("✅ FlowGenerator方法存在性测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_communication_simulator_methods():
    """测试CommunicationSimulator的方法"""
    print("\n=== CommunicationSimulator方法测试 ===")
    
    try:
        from collective_communication.communication_simulator import CommunicationSimulator
        
        comm_simulator = CommunicationSimulator()
        
        # 检查新添加的方法是否存在
        methods_to_check = [
            '_calculate_ring_size',
            '_calculate_computation_time',
            '_start_new_allreduce_cycle',
            '_check_and_restart_allreduce',
            '_handle_allreduce_completion',
            '_execute_multiple_allreduces',
            '_calculate_average_communication_times'
        ]
        
        print("检查CommunicationSimulator方法:")
        for method_name in methods_to_check:
            if hasattr(comm_simulator, method_name):
                print(f"  ✅ {method_name} 方法存在")
            else:
                print(f"  ❌ {method_name} 方法不存在")
                return False
        
        print("✅ CommunicationSimulator方法测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_method_call_syntax():
    """测试方法调用语法是否正确"""
    print("\n=== 方法调用语法测试 ===")
    
    try:
        from collective_communication.communication_simulator import CommunicationSimulator
        
        comm_simulator = CommunicationSimulator()
        
        # 创建模拟作业
        class MockJob:
            def __init__(self, name):
                self.name = name
                self.placement = (2, 2)
                self.application = MockApplication()
        
        class MockApplication:
            def get_throughput(self, placement, atomic_bsz):
                return (2.0, 0.5)
        
        job = MockJob("test_job")
        
        # 测试_calculate_ring_size方法
        try:
            ring_size = comm_simulator._calculate_ring_size(job)
            print(f"  ✅ _calculate_ring_size调用成功，结果: {ring_size}")
        except Exception as e:
            print(f"  ❌ _calculate_ring_size调用失败: {e}")
            return False
        
        # 测试_calculate_computation_time方法
        try:
            comp_time = comm_simulator._calculate_computation_time(job)
            print(f"  ✅ _calculate_computation_time调用成功，结果: {comp_time:.4f}秒")
        except Exception as e:
            print(f"  ❌ _calculate_computation_time调用失败: {e}")
            return False
        
        # 测试_calculate_average_communication_times方法
        try:
            # 设置一些模拟数据
            comm_simulator.completed_allreduces = {
                'test_job': [1.5, 1.6, 1.4]
            }
            avg_times = comm_simulator._calculate_average_communication_times()
            expected_avg = (1.5 + 1.6 + 1.4) / 3
            print(f"  ✅ _calculate_average_communication_times调用成功")
            print(f"    输入: [1.5, 1.6, 1.4], 输出: {avg_times.get('test_job', 0):.4f}, 期望: {expected_avg:.4f}")
            
            if abs(avg_times.get('test_job', 0) - expected_avg) < 0.001:
                print(f"    ✅ 平均值计算正确")
            else:
                print(f"    ❌ 平均值计算错误")
                return False
                
        except Exception as e:
            print(f"  ❌ _calculate_average_communication_times调用失败: {e}")
            return False
        
        print("✅ 方法调用语法测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_code_structure_integrity():
    """测试代码结构完整性"""
    print("\n=== 代码结构完整性测试 ===")
    
    try:
        # 测试导入是否正常
        from collective_communication.communication_simulator import CommunicationSimulator
        from collective_communication.flow_generator import FlowGenerator
        from collective_communication.event_manager import Event, EventType
        from collective_communication.ring_all_reduce_tracker import RingAllReduceTracker
        
        print("  ✅ 所有核心模块导入成功")
        
        # 测试基本实例化
        flow_generator = FlowGenerator()
        comm_simulator = CommunicationSimulator()
        
        print("  ✅ 核心类实例化成功")
        
        # 测试多次Ring All-Reduce控制字段
        required_fields = [
            'enable_multiple_allreduces',
            'completed_allreduces',
            'allreduce_count',
            'target_allreduce_counts',
            'current_placement'
        ]
        
        for field in required_fields:
            if hasattr(comm_simulator, field):
                print(f"  ✅ {field} 字段存在")
            else:
                print(f"  ❌ {field} 字段不存在")
                return False
        
        print("✅ 代码结构完整性测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("开始简单FlowGenerator修复验证测试...")
    
    tests = [
        test_flow_generator_method_exists,
        test_communication_simulator_methods,
        test_method_call_syntax,
        test_code_structure_integrity
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {e}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有简单FlowGenerator修复测试通过！")
        print("\n✅ 验证的修复：")
        print("  1. ✅ FlowGenerator.generate_first_round_flows方法确实不存在")
        print("  2. ✅ FlowGenerator.generate_next_round_flows方法存在且可用")
        print("  3. ✅ CommunicationSimulator的所有新方法都存在")
        print("  4. ✅ 方法调用语法正确，不会出现AttributeError")
        print("  5. ✅ 多次Ring All-Reduce的核心结构完整")
        print("\n🎯 修复后的调用方式:")
        print("     generate_next_round_flows(job, -1, topology_manager)  # 生成第0轮（第一轮）")
        return True
    else:
        print("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

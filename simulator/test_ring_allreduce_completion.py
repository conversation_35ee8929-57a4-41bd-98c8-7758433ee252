#!/usr/bin/env python3
"""
测试 Ring All-Reduce 完成逻辑
专门测试为什么 Ring All-Reduce 没有完成
"""

import sys
import os
import logging
sys.path.append('.')

# 启用INFO级别日志，避免过多DEBUG信息
logging.basicConfig(level=logging.INFO)

def test_ring_allreduce_completion():
    """测试 Ring All-Reduce 完成逻辑"""
    print("🔍 测试 Ring All-Reduce 完成逻辑")
    
    try:
        # 导入必要的模块
        from collective_communication.communication_simulator import CommunicationSimulator
        from collective_communication.flow_generator import FlowGenerator
        from routing.topology_manager import TopologyManager
        from routing.prediction_service import PredictionService
        from routing.routing_optimizer import RoutingOptimizer
        
        # 创建模拟作业
        class MockJob:
            def __init__(self, name):
                self.name = name
                self.placement = (2, 2)  # 2个GPU在节点0，2个GPU在节点1（多节点）
                self.atomic_bsz = 32
                
                # 创建模拟应用
                class MockApplication:
                    def __init__(self):
                        self.name = "cifar10"
                        self.max_batch_size = 2048
                        self.min_local_bsz = 32
                        self.max_local_bsz = 512
                        self.init_batch_size = 128
                        
                    def get_throughput(self, placement, atomic_bsz):
                        # 返回 (step_time, sync_time)
                        return (1.0, 0.1)  # 1秒总时间，0.1秒通信时间
                        
                self.application = MockApplication()
        
        # 初始化组件
        print("初始化通信模拟器...")
        topology_manager = TopologyManager()
        prediction_service = PredictionService()
        flow_generator = FlowGenerator()
        routing_optimizer = RoutingOptimizer(prediction_service)
        
        comm_simulator = CommunicationSimulator(
            flow_generator, topology_manager, prediction_service,
            enable_routing_optimizer=False
        )
        comm_simulator.set_routing_optimizer(routing_optimizer)

        # 启用批量预测来测试修复
        comm_simulator.enable_batch_prediction = True
        
        # 创建测试作业
        job = MockJob("test_job")
        active_jobs = [job]
        
        # 注册作业
        comm_simulator.job_registry[job.name] = job
        comm_simulator.current_placement[job.name] = [0, 0, 1, 1]  # 2个GPU在节点0，2个GPU在节点1
        
        print(f"创建测试作业: {job.name}")
        print(f"作业placement: {job.placement}")
        print(f"作业atomic_bsz: {job.atomic_bsz}")
        
        # 生成流
        print("\n生成流...")
        all_potential_flows = flow_generator.generate_all_potential_flows(
            active_jobs, {job.name: [0, 0, 1, 1]}, topology_manager
        )
        
        if not all_potential_flows:
            print("❌ 没有生成任何流")
            return False
            
        print(f"生成了 {len(all_potential_flows)} 个流")
        
        # 设置流到通信模拟器
        comm_simulator.set_all_known_flows(all_potential_flows)
        comm_simulator.enable_multiple_allreduces = True
        
        # 执行通信模拟
        print("\n执行通信模拟...")
        final_routing_plan = {}
        for flow in all_potential_flows:
            flow_id = flow.get("flow_id")
            if flow.get("candidate_paths"):
                final_routing_plan[flow_id] = flow["candidate_paths"][0]
            else:
                final_routing_plan[flow_id] = []
        
        # 运行模拟
        interval_duration = 10.0  # 10秒间隔
        communication_times = comm_simulator.run(final_routing_plan, active_jobs, interval_duration)
        
        print(f"\n通信模拟结果: {communication_times}")
        
        # 检查结果
        if job.name in communication_times:
            comm_time = communication_times[job.name]
            allreduce_count = comm_simulator.allreduce_count.get(job.name, 0)
            
            print(f"作业 {job.name}:")
            print(f"  通信时间: {comm_time:.4f}秒")
            print(f"  完成次数: {allreduce_count}")
            
            if allreduce_count > 0:
                print("✅ Ring All-Reduce 成功完成")
                return True
            else:
                print("❌ Ring All-Reduce 没有完成")
                
                # 调试信息
                print("\n调试信息:")
                print(f"  completed_allreduces: {comm_simulator.completed_allreduces}")
                print(f"  tracker job_status: {comm_simulator.tracker.job_status}")
                print(f"  tracker job_communication_times: {comm_simulator.tracker.job_communication_times}")
                print(f"  tracker job_total_rounds: {comm_simulator.tracker.job_total_rounds}")
                print(f"  tracker job_current_rounds: {comm_simulator.tracker.job_current_rounds}")
                print(f"  tracker job_round_status: {comm_simulator.tracker.job_round_status}")
                print(f"  tracker active_flows: {list(comm_simulator.tracker.active_flows.keys())}")
                print(f"  tracker completed_flows: {list(comm_simulator.tracker.completed_flows.keys())}")
                print(f"  tracker flow_to_job: {comm_simulator.tracker.flow_to_job}")
                print(f"  tracker flow_to_round: {comm_simulator.tracker.flow_to_round}")

                return False
        else:
            print(f"❌ 作业 {job.name} 没有在结果中找到")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ring_allreduce_completion()
    if success:
        print("\n🎉 Ring All-Reduce 完成逻辑测试通过！")
    else:
        print("\n💥 Ring All-Reduce 完成逻辑测试失败！")
    
    sys.exit(0 if success else 1)

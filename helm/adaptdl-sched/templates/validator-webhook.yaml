apiVersion: v1
kind: Secret
type: kubernetes.io/tls
metadata:
  name: {{ .Release.Name }}-validator
  annotations:
    "helm.sh/hook": "pre-install"
    "helm.sh/hook-delete-policy": "before-hook-creation"
data:
{{- $altNames := list ( printf "%s-validator.%s" .Release.Name .Release.Namespace ) ( printf "%s-validator.%s.svc" .Release.Name .Release.Namespace ) -}}
{{- $cert := genSelfSignedCert ( printf "%s-validator" .Release.Name ) nil $altNames 3650 }}
  tls.crt: {{ $cert.Cert | b64enc }}
  tls.key: {{ $cert.Key | b64enc }}
---
apiVersion: admissionregistration.k8s.io/v1beta1
kind: ValidatingWebhookConfiguration
metadata:
  name: {{ .Release.Name }}-validator
  labels:
    app: {{ .Release.Name }}-validator
  annotations:
    "helm.sh/hook": "pre-install"
    "helm.sh/hook-delete-policy": "before-hook-creation"
webhooks:
  - name: {{ .Release.Name }}-validator.{{ .Release.Namespace }}.svc.cluster.local
    clientConfig:
      caBundle: {{ $cert.Cert | b64enc }}
      service:
        name: {{ .Release.Name }}-validator
        namespace: {{ .Release.Namespace }}
        path: "/validate"
    rules:
      - operations: ["CREATE", "UPDATE"]
        apiGroups: ["adaptdl.petuum.com"]
        apiVersions: ["v1beta1"]
        resources: ["adaptdljobs"]
    admissionReviewVersions:
      - v1beta1
    sideEffects: None

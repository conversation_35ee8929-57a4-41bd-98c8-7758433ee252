apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  creationTimestamp: null
  name: adaptdl-role-1
rules:
- apiGroups: ["", "apps", "extensions", "adaptdl.petuum.com"]
  resources: ["*"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  creationTimestamp: null
  name: test-adaptdl-rolebinding-1
  namespace: default
roleRef:
  kind: ClusterRole
  name: adaptdl-role-1
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: default
  namespace: {{.Release.Namespace}}

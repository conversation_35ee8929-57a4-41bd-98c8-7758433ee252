apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  name: adaptdljobs.adaptdl.petuum.com
spec:
  group: adaptdl.petuum.com
  versions:
    - name: v1
      served: true
      storage: true
  scope: Namespaced
  names:
    plural: adaptdljobs
    singular: adaptdljob
    kind: AdaptDLJob
  additionalPrinterColumns:
    - name: Ready
      type: integer
      JSONPath: .status.readyReplicas
    - name: Replicas
      type: string
      JSONPath: .status.replicas
    - name: Restarts
      type: integer
      JSONPath: .status.group
    - name: Status
      type: string
      JSONPath: .status.phase
    - name: Age
      type: date
      JSONPath: .metadata.creationTimestamp
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      type: object
      properties:
        metadata:
          type: object
          properties:
            # Name is used as label values which have a 63 character limit.
            name:
              type: string
              maxLength: 63
        spec:
          type: object
          properties:
            maxReplicas:
              type: integer
              minimum: 1
            minReplicas:
              type: integer
              minimum: 0
            preemptible:
              type: boolean
            template:
              type: object
          required: ["template"]
      required: ["spec"]

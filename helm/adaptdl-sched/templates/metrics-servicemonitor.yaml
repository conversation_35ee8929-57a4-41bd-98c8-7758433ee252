{{- if .Values.metrics.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ .Release.Name }}-adaptdl-sched
  {{- if .Values.metrics.serviceMonitor.namespace }}
  namespace: {{ .Values.metrics.serviceMonitor.namespace }}
  {{- end }}
  labels:
    {{- range $key, $value := .Values.metrics.serviceMonitor.selector }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
spec:
  endpoints:
  - port: http
    {{- if .Values.metrics.serviceMonitor.interval }}
    interval: {{ .Values.metrics.serviceMonitor.interval }}
    {{- end }}
    honorLabels: {{ .Values.metrics.serviceMonitor.honorLabels }}
  selector:
    matchLabels:
      app: adaptdl-sched
      release: {{ .Release.Name }}
  namespaceSelector:
    matchNames:
    - {{ .Release.Namespace }}
{{- end -}}

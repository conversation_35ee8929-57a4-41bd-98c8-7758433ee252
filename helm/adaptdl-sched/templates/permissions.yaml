apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: adaptdl-anon
rules:
- apiGroups: ["adaptdl.petuum.com"] # "" indicates the core API group
  resources: ["adaptdljobs", "adaptdljobs/status"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
# This role binding allows "jane" to read pods in the "default" namespace.
kind: RoleBinding
metadata:
  name: adaptdl-anon
subjects:
- kind: User
  name: system:anonymous # Name is case sensitive
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: Role #this must be Role or ClusterRole
  name: adaptdl-anon # this must match the name of the Role or ClusterRole you wish to bind to
  apiGroup: rbac.authorization.k8s.io

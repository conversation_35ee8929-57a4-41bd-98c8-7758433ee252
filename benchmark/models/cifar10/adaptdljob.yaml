apiVersion: adaptdl.petuum.com/v1
kind: AdaptDLJob
metadata:
  generateName: cifar10-
spec:
  template:
    spec:
      containers:
      - name: main
        command:
        - python3
        - /root/cifar10/main.py
        - --model=ResNet18
        env:
        - name: PYTHONUNBUFFERED
          value: "true"
        resources:
          limits:
            nvidia.com/gpu: 1
            cpu: 10
            memory: "40G"
          requests:
            nvidia.com/gpu: 1
            cpu: 10
            memory: "40G"
        volumeMounts:
        - name: data
          mountPath: /mnt
          readOnly: true
      volumes:
      - name: data
        hostPath:
          path: /mnt
          type: Directory
      imagePullSecrets:
      - name: regcred

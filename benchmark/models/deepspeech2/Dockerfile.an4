# Esper setup
FROM python:3.7-slim
WORKDIR /root

COPY README.md requirements.txt requirements-sched.txt setup.py ./
COPY esper esper
COPY clustersim clustersim
COPY bin/esperctl bin/esperctl

ARG ESPER_VERSION=0.0.0
RUN ESPER_VERSION=${ESPER_VERSION} python setup.py sdist bdist_wheel

# Deepspeech 2 setup
FROM nvcr.io/nvidia/pytorch:20.03-py3
WORKDIR /workspace/

# install basics
RUN apt-get update -y
RUN apt-get install -y git curl ca-certificates bzip2 cmake tree htop bmon iotop sox libsox-dev libsox-fmt-all vim

# install python deps
RUN pip install cython visdom cffi tensorboardX wget matplotlib

# install pytorch audio
RUN git clone https://github.com/pytorch/audio.git
RUN cd audio; python setup.py install

# install ctcdecode
#RUN git clone --recursive https://github.com/parlance/ctcdecode.git
#RUN cd ctcdecode; pip install .

ENV LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH

# install apex
RUN git clone --recursive https://github.com/NVIDIA/apex.git
RUN cd apex; pip install .

# install esper
COPY --from=0 /root/dist dist
RUN pip install tensorboard
RUN pip install dist/esper-*.whl && rm -r dist

# install deepspeech.pytorch
COPY apps/deepspeech2 /workspace/deepspeech.pytorch
RUN cd deepspeech.pytorch; pip install -r requirements.txt

# change workdir to start in the directory with train.py
WORKDIR /workspace/deepspeech.pytorch

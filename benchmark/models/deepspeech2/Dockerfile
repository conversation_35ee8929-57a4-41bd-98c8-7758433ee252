FROM nvcr.io/nvidia/pytorch:20.10-py3
WORKDIR /root

COPY adaptdl/requirements.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt
COPY benchmark/models/deepspeech2/requirements.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

COPY adaptdl adaptdl
ENV PYTHONPATH=/root/adaptdl:$PYTHONPATH

COPY benchmark/models/deepspeech2 deepspeech2
ENV PYTHONPATH=/root/depspeech2:$PYTHONPATH
WORKDIR /root/deepspeech2

ENV PYTHONUNBUFFERED=true

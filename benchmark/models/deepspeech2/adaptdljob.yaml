apiVersion: adaptdl.petuum.com/v1
kind: AdaptDLJob
metadata:
  generateName: deepspeech2-
spec:
  template:
    spec:
      containers:
      - name: main
        command:
        - python
        - /root/deepspeech2/train.py
        - --cuda
        - --train-manifest=/mnt/deepspeech2_data/voxforge_cmu_us_train_manifest_part1.csv
        - --val-manifest=/mnt/deepspeech2_data/voxforge_cmu_us_train_manifest_part2.csv
        - --max-norm=1
        - --batch-size=20
        - --lr=0.12
        - --learning-anneal=1.02
        - --momentum=0.95
        - --epochs=80
        env:
        - name: PYTHONUNBUFFERED
          value: "true"
        resources:
          limits:
            nvidia.com/gpu: 1
            cpu: 10
            memory: "40G"
          requests:
            nvidia.com/gpu: 1
            cpu: 10
            memory: "40G"
        volumeMounts:
        - name: data
          mountPath: /mnt
          readOnly: true
      volumes:
      - name: data
        hostPath:
          path: /mnt
          type: Directory
      imagePullSecrets:
      - name: regcred

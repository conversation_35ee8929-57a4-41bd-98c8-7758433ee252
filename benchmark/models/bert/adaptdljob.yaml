apiVersion: adaptdl.petuum.com/v1
kind: AdaptDLJob
metadata:
  generateName: bert-
spec:
  template:
    spec:
      terminationGracePeriodSeconds: 60
      containers:
      - name: main
        command:
        - python3
        - run_squad.py
        - --model_type=bert
        - --model_name_or_path=bert-base-uncased
        - --do_train
        - --do_lower_case
        - --train_file=/mnt/squad/train-v1.1.json
        - --predict_file=/mnt/squad/dev-v1.1.json
        - --per_gpu_train_batch_size=12
        - --learning_rate=3e-5
        - --num_train_epochs=2.0
        - --max_seq_length=384
        - --doc_stride=128
        - --cache_dir=/mnt/squad
        - --data_dir=/mnt/squad
        - --output_dir=/tmp/output
        env:
        - name: PYTHONUNBUFFERED
          value: "true"
        resources:
          limits:
            nvidia.com/gpu: 1
            cpu: 10
            memory: "40G"
          requests:
            nvidia.com/gpu: 1
            cpu: 10
            memory: "40G"
        volumeMounts:
        - name: data
          mountPath: /mnt
      volumes:
      - name: data
        hostPath:
          path: /mnt
          type: Directory
      imagePullSecrets:
      - name: regcred

apiVersion: adaptdl.petuum.com/v1
kind: AdaptDLJob
metadata:
  generateName: ncf-
spec:
  maxReplicas: 1
  template:
    spec:
      containers:
      - name: main
        command:
        - python3
        - /root/ncf/main.py
        - --epochs=10
        env:
        - name: PYTHONUNBUFFERED
          value: "true"
        - name: TARGET_BATCH_SIZE
          value: "32768"
        resources:
          limits:
            nvidia.com/gpu: 1
            cpu: 10
            memory: "40G"
          requests:
            nvidia.com/gpu: 1
            cpu: 10
            memory: "40G"
        volumeMounts:
        - name: data
          mountPath: /mnt
          readOnly: true
      volumes:
      - name: data
        hostPath:
          path: /mnt
          type: Directory
      imagePullSecrets:
      - name: regcred

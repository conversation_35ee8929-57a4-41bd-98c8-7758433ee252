name: pollux
channels:
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - ca-certificates=2020.10.14=0
  - certifi=2020.6.20=pyhd3eb1b0_3
  - ld_impl_linux-64=2.33.1=h53a641e_7
  - libedit=3.1.20191231=h14c3975_1
  - libffi=3.3=he6710b0_2
  - libgcc-ng=9.1.0=hdf63c60_0
  - libstdcxx-ng=9.1.0=hdf63c60_0
  - ncurses=6.2=he6710b0_1
  - openssl=1.1.1h=h7b6447c_0
  - pip=20.2.4=py38h06a4308_0
  - python=3.8.5=h7579374_1
  - readline=8.0=h7b6447c_0
  - setuptools=50.3.1=py38h06a4308_1
  - sqlite=3.33.0=h62c20be_0
  - tk=8.6.10=hbc83047_0
  - wheel=0.35.1=pyhd3eb1b0_0
  - xz=5.2.5=h7b6447c_0
  - zlib=1.2.11=h7b6447c_3
  - pip:
    - asgiref==3.3.1
    - blinker==1.4
    - brotli==1.0.9
    - cachetools==4.1.1
    - cffi==1.14.3
    - chardet==3.0.4
    - click==7.1.2
    - cryptography==3.2.1
    - flask==1.1.2
    - google-auth==1.23.0
    - h11==0.11.0
    - h2==4.0.0
    - hpack==4.0.0
    - hyperframe==6.0.0
    - idna==2.10
    - itsdangerous==1.1.0
    - jinja2==2.11.2
    - kaitaistruct==0.9
    - kubernetes==12.0.1
    - ldap3==2.8.1
    - markupsafe==1.1.1
    - mitmproxy==5.3.0
    - msgpack==1.0.0
    - numpy==1.19.4
    - oauthlib==3.1.0
    - pandas==1.1.4
    - passlib==1.7.4
    - portpicker==1.3.1
    - protobuf==3.13.0
    - publicsuffix2==2.20191221
    - pyasn1==0.4.8
    - pyasn1-modules==0.2.8
    - pycparser==2.20
    - pyopenssl==19.1.0
    - pyparsing==2.4.7
    - pyperclip==1.8.1
    - python-dateutil==2.8.1
    - pytz==2020.4
    - pyyaml==5.3.1
    - requests==2.25.0
    - requests-oauthlib==1.3.0
    - rsa==4.6
    - ruamel.yaml==0.16.12
    - ruamel.yaml.clib==0.2.2
    - six==1.15.0
    - sortedcontainers==2.2.2
    - tornado==6.1
    - urllib3==1.26.2
    - urwid==2.1.2
    - websocket-client==0.57.0
    - werkzeug==1.0.1
    - wsproto==0.15.0
    - zstandard==0.14.0
prefix: /home/<USER>/anaconda3/envs/pollux

